"""Tests for kmer_finder module."""

import pytest
from Bio.Seq import Seq
from ppdesign.kmer_finder import canonical_kmer, count_kmers


class TestCanonicalKmer:
    """Test canonical k-mer computation."""
    
    def test_canonical_kmer_basic(self):
        """Test basic canonical k-mer functionality."""
        assert canonical_kmer("ATCG") == "ATCG"
        assert canonical_kmer("CGAT") == "ATCG"  # Reverse complement
        
    def test_canonical_kmer_palindrome(self):
        """Test palindromic sequences."""
        assert canonical_kmer("ATAT") == "ATAT"
        assert canonical_kmer("GCGC") == "GCGC"
        
    def test_canonical_kmer_reverse(self):
        """Test that reverse is considered."""
        kmer = "AAAG"
        rev = "GAAA"
        rev_comp = "CTTT"
        assert canonical_kmer(kmer) == "AAAG"  # Original is smallest
        

class TestCountKmers:
    """Test k-mer counting functionality."""
    
    def test_count_kmers_simple(self):
        """Test basic k-mer counting."""
        sequence = "ATCGATCG"
        k_values = [3]
        counts = count_kmers(sequence, k_values)
        
        assert len(counts) == 1
        assert 3 in counts
        
        # Check specific k-mers
        kmer_counts = counts[3]
        assert kmer_counts[canonical_kmer("ATC")] == 2  # Appears twice
        assert kmer_counts[canonical_kmer("TCG")] == 2  # Appears twice
        
    def test_count_kmers_with_n(self):
        """Test that k-mers with N are skipped."""
        sequence = "ATCNATCG"
        k_values = [3]
        counts = count_kmers(sequence, k_values)
        
        kmer_counts = counts[3]
        # "TCN" and "CNA" and "NAT" should be skipped
        assert "TCN" not in str(kmer_counts)
        assert kmer_counts[canonical_kmer("ATC")] == 1  # Only one valid occurrence
        
    def test_count_kmers_multiple_k(self):
        """Test counting with multiple k values."""
        sequence = "ATCGATCG"
        k_values = [2, 3, 4]
        counts = count_kmers(sequence, k_values)
        
        assert len(counts) == 3
        assert all(k in counts for k in k_values)
        
        # Check counts for different k values
        assert len(counts[2]) > 0
        assert len(counts[3]) > 0
        assert len(counts[4]) > 0