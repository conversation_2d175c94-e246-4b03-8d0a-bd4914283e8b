#!/bin/bash
# Start Jupyter Lab for PPDesign accessible via Tailscale network

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get Tailscale IP and hostname
TAILSCALE_IP=$(tailscale ip -4 2>/dev/null || echo "")
HOSTNAME=$(hostname)
MACHINE_NAME=$(tailscale status --json 2>/dev/null | jq -r '.Self.DNSName' | cut -d'.' -f1 || echo $HOSTNAME)

# Check if Tailscale is running
if [ -z "$TAILSCALE_IP" ]; then
    echo -e "${RED}❌ Tailscale is not running or not configured${NC}"
    echo "Please start Tailscale first: sudo tailscale up"
    exit 1
fi

# Default configuration
PORT=${JUPYTER_PORT:-8888}
NOTEBOOK_DIR=${NOTEBOOK_DIR:-$(pwd)}

echo -e "${BLUE}=================================================="
echo -e "       PPDesign Jupyter Lab Server"
echo -e "==================================================${NC}"
echo -e "${GREEN}✓ Tailscale IP:${NC} $TAILSCALE_IP"
echo -e "${GREEN}✓ Machine Name:${NC} $MACHINE_NAME"
echo -e "${GREEN}✓ Port:${NC} $PORT"
echo -e "${GREEN}✓ Notebook Directory:${NC} $NOTEBOOK_DIR"
echo ""
echo -e "${YELLOW}📡 Access URLs for Tailscale network members:${NC}"
echo -e "  ${BLUE}http://$TAILSCALE_IP:$PORT${NC}"
echo -e "  ${BLUE}http://$MACHINE_NAME:$PORT${NC}"
echo -e "  ${BLUE}http://$HOSTNAME:$PORT${NC}"
echo ""
echo -e "${YELLOW}🔐 Security Note:${NC}"
echo "  First-time users will need a token (shown below)"
echo "  Or set a password with: jupyter lab password"
echo -e "${BLUE}==================================================${NC}"
echo ""

# Check if running in pixi environment
if command -v pixi &> /dev/null; then
    echo -e "${GREEN}✓ Using pixi environment${NC}"
    echo ""
    
    # Start Jupyter Lab with pixi
    pixi run jupyter lab \
        --ip=$TAILSCALE_IP \
        --port=$PORT \
        --no-browser \
        --allow-root \
        --ServerApp.allow_origin='*' \
        --ServerApp.allow_remote_access=True \
        --ServerApp.disable_check_xsrf=True \
        --notebook-dir="$NOTEBOOK_DIR"
else
    echo -e "${YELLOW}⚠ Pixi not found, using system Jupyter${NC}"
    echo ""
    
    # Start Jupyter Lab without pixi
    jupyter lab \
        --ip=$TAILSCALE_IP \
        --port=$PORT \
        --no-browser \
        --allow-root \
        --NotebookApp.allow_origin='*' \
        --NotebookApp.allow_remote_access=True \
        --NotebookApp.disable_check_xsrf=True \
        --notebook-dir="$NOTEBOOK_DIR"
fi