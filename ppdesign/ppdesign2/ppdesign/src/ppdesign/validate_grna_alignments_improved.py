#!/usr/bin/env python3
"""
Improved validation for guide RNA alignments with correct parasail handling.

Key improvements:
1. Correct identity calculation for semi-global alignments
2. Proper position extraction from parasail results
3. Strand-aware reporting for cDNA applications
"""

import argparse
import json
from pathlib import Path
from typing import Dict, List, Optional
import numpy as np
import pandas as pd
from Bio import SeqIO
from Bio.Seq import Seq
import parasail
from rich.console import Console
from rich.table import Table
from rich.progress import track
from dataclasses import dataclass, field


@dataclass
class ImprovedAlignmentResult:
    """Enhanced alignment result with proper parasail metrics."""

    seq_id: str
    strand: str  # '+' or '-'
    score: int
    identity: float  # Properly calculated
    similarity: float  # Based on substitution matrix
    coverage: float
    start_pos: int
    end_pos: int
    mismatches: List[int] = field(default_factory=list)
    gaps: int = 0
    cigar: str = ""
    ref_aligned: str = ""
    query_aligned: str = ""
    alignment_length: int = 0

    @property
    def has_seed_mismatches(self) -> bool:
        """Check for mismatches in seed region (positions 10-17 for SpCas9)."""
        seed_positions = set(range(10, 18))  # 0-indexed positions 10-17
        return bool(set(self.mismatches) & seed_positions)

    @property
    def has_pam_proximal_mismatches(self) -> bool:
        """Check for mismatches near PAM (positions 17-19)."""
        pam_proximal = set(range(17, 20))
        return bool(set(self.mismatches) & pam_proximal)

    @property
    def cfd_score(self) -> float:
        """Calculate Cutting Frequency Determination (CFD) score."""
        # Simplified CFD calculation - in production, use full matrix
        if not self.mismatches:
            return 1.0

        # Position-specific penalties (simplified)
        score = 1.0
        for pos in self.mismatches:
            if pos < 10:  # PAM-distal
                score *= 0.8
            elif pos < 17:  # Seed region
                score *= 0.3
            else:  # PAM-proximal
                score *= 0.5

        return score


class ImprovedGuideRNAValidator:
    """Enhanced validator with correct parasail usage and cDNA considerations."""

    def __init__(
        self,
        guide_csv: Path,
        guide_fasta: Path,
        target_fasta: Path,
        target_type: str = "dna",
    ):
        """
        Initialize validator.

        Args:
            guide_csv: Path to guide RNA CSV
            guide_fasta: Path to guide RNA FASTA
            target_fasta: Path to target sequences
            target_type: 'dna' or 'cdna' - affects strand interpretation
        """
        self.console = Console()
        self.guide_csv = guide_csv
        self.guide_fasta = guide_fasta
        self.target_fasta = target_fasta
        self.target_type = target_type

        # Load data
        self.guides_df = pd.read_csv(guide_csv)
        self.guides = self._load_guides()
        self.targets = self._load_targets()

        # Alignment parameters optimized for guide RNAs
        self.matrix = parasail.matrix_create("ACGT", 3, -2)  # Higher match score
        self.gap_open = 5  # Higher gap penalty for guide RNAs
        self.gap_extend = 2

    def _load_guides(self) -> Dict[str, str]:
        """Load guide RNA sequences from FASTA."""
        guides = {}
        for record in SeqIO.parse(self.guide_fasta, "fasta"):
            guides[record.id] = str(record.seq).upper()
        return guides

    def _load_targets(self) -> Dict[str, str]:
        """Load target sequences from FASTA."""
        targets = {}
        for record in SeqIO.parse(self.target_fasta, "fasta"):
            targets[record.id] = str(record.seq).upper()
        return targets

    def align_guide_to_target_improved(
        self, guide_seq: str, target_seq: str, strand: str
    ) -> Optional[ImprovedAlignmentResult]:
        """
        Perform proper semi-global alignment with correct metric calculation.

        Args:
            guide_seq: Guide RNA sequence (20bp)
            target_seq: Target sequence
            strand: '+' or '-' indicating which strand

        Returns:
            ImprovedAlignmentResult with correct metrics
        """
        # Use semi-global alignment with statistics and traceback
        result = parasail.sg_stats_trace_scan(
            guide_seq, target_seq, self.gap_open, self.gap_extend, self.matrix
        )

        # Check if alignment meets minimum threshold
        if result.score < len(guide_seq) * 2:  # Minimum score threshold
            return None

        # Get traceback for detailed alignment
        cigar = result.cigar
        trace = result.traceback

        ref_aligned = trace.ref
        query_aligned = trace.query

        # Calculate proper metrics
        matches = 0
        mismatches = []
        gaps = 0
        query_pos = 0

        for i, (r, q) in enumerate(zip(ref_aligned, query_aligned)):
            if q == "-":
                gaps += 1
            else:
                if r == q:
                    matches += 1
                elif r != "-":
                    mismatches.append(query_pos)
                query_pos += 1

        # Calculate identity properly
        alignment_length = len([c for c in query_aligned if c != "-"])
        identity = (matches / alignment_length * 100) if alignment_length > 0 else 0

        # Calculate similarity (considers substitution matrix scores)
        similarity = (
            (result.matches / alignment_length * 100) if alignment_length > 0 else 0
        )

        # Calculate coverage
        coverage = (alignment_length / len(guide_seq)) * 100

        # Get correct positions from parasail result
        # Note: parasail reports end positions, need to calculate start
        ref_end = result.end_ref
        ref_aligned_len = len([c for c in ref_aligned if c != "-"])
        ref_start = ref_end - ref_aligned_len + 1

        # Adjust positions for reverse strand
        if strand == "-":
            # Convert to forward strand coordinates
            seq_len = len(target_seq)
            actual_start = seq_len - ref_end - 1
            actual_end = seq_len - ref_start - 1
            ref_start, ref_end = actual_start, actual_end

        return ImprovedAlignmentResult(
            seq_id="",  # Set by caller
            strand=strand,
            score=result.score,
            identity=identity,
            similarity=similarity,
            coverage=coverage,
            start_pos=ref_start,
            end_pos=ref_end,
            mismatches=mismatches,
            gaps=gaps,
            cigar=cigar.decode("utf-8") if isinstance(cigar, bytes) else str(cigar),
            ref_aligned=ref_aligned,
            query_aligned=query_aligned,
            alignment_length=alignment_length,
        )

    def validate_guide_with_strand_awareness(
        self, guide_id: str, guide_seq: str
    ) -> Dict:
        """
        Validate guide RNA with proper strand awareness for cDNA.

        For cDNA targets:
        - Forward strand represents the mRNA-derived sequence
        - Reverse strand would be the antisense
        """
        results = {
            "guide_id": guide_id,
            "sequence": guide_seq,
            "length": len(guide_seq),
            "alignments": [],
            "forward_strand_hits": 0,
            "reverse_strand_hits": 0,
            "total_hits": 0,
            "perfect_matches": 0,
            "seed_mismatches": 0,
            "pam_proximal_mismatches": 0,
            "avg_identity": 0,
            "avg_cfd_score": 0,
            "conservation": 0,
            "strand_bias": None,  # 'forward', 'reverse', or 'balanced'
        }

        # Track alignments by strand
        forward_aligns = []
        reverse_aligns = []

        # Align against all targets
        for target_id, target_seq in self.targets.items():
            # Check forward strand
            fwd_align = self.align_guide_to_target_improved(guide_seq, target_seq, "+")
            if fwd_align and fwd_align.identity >= 75:  # Lower threshold for detection
                fwd_align.seq_id = f"{target_id}_forward"
                results["alignments"].append(fwd_align)
                forward_aligns.append(fwd_align)
                results["forward_strand_hits"] += 1

                if fwd_align.identity == 100:
                    results["perfect_matches"] += 1
                if fwd_align.has_seed_mismatches:
                    results["seed_mismatches"] += 1
                if fwd_align.has_pam_proximal_mismatches:
                    results["pam_proximal_mismatches"] += 1

            # Check reverse strand
            rev_seq = str(Seq(target_seq).reverse_complement())
            rev_align = self.align_guide_to_target_improved(guide_seq, rev_seq, "-")
            if rev_align and rev_align.identity >= 75:
                rev_align.seq_id = f"{target_id}_reverse"
                results["alignments"].append(rev_align)
                reverse_aligns.append(rev_align)
                results["reverse_strand_hits"] += 1

                if rev_align.identity == 100:
                    results["perfect_matches"] += 1
                if rev_align.has_seed_mismatches:
                    results["seed_mismatches"] += 1
                if rev_align.has_pam_proximal_mismatches:
                    results["pam_proximal_mismatches"] += 1

        # Calculate statistics
        results["total_hits"] = (
            results["forward_strand_hits"] + results["reverse_strand_hits"]
        )

        if results["alignments"]:
            results["avg_identity"] = np.mean(
                [a.identity for a in results["alignments"]]
            )
            results["avg_cfd_score"] = np.mean(
                [a.cfd_score for a in results["alignments"]]
            )

            # Calculate conservation (sequences with at least one hit)
            unique_targets = set(
                a.seq_id.rsplit("_", 1)[0] for a in results["alignments"]
            )
            results["conservation"] = (len(unique_targets) / len(self.targets)) * 100

            # Determine strand bias
            if results["forward_strand_hits"] > results["reverse_strand_hits"] * 1.5:
                results["strand_bias"] = "forward"
            elif results["reverse_strand_hits"] > results["forward_strand_hits"] * 1.5:
                results["strand_bias"] = "reverse"
            else:
                results["strand_bias"] = "balanced"

        return results

    def generate_enhanced_report(self, results: List[Dict], output_file: Path):
        """Generate comprehensive report with strand awareness and CFD scores."""
        self.console.print("\n[bold green]Enhanced Validation Report[/bold green]\n")

        # Create enhanced summary table
        table = Table(title="Guide RNA Validation with Strand Analysis")
        table.add_column("Guide ID", style="cyan")
        table.add_column("Conservation", justify="right", style="yellow")
        table.add_column("Avg Identity", justify="right", style="green")
        table.add_column("CFD Score", justify="right", style="magenta")
        table.add_column("Forward\nHits", justify="right")
        table.add_column("Reverse\nHits", justify="right")
        table.add_column("Strand\nBias", style="blue")
        table.add_column("Seed\nMismatches", justify="right", style="red")

        for result in results:
            table.add_row(
                result["guide_id"],
                f"{result['conservation']:.1f}%",
                f"{result['avg_identity']:.1f}%",
                f"{result['avg_cfd_score']:.3f}",
                str(result["forward_strand_hits"]),
                str(result["reverse_strand_hits"]),
                result["strand_bias"] or "N/A",
                str(result["seed_mismatches"]),
            )

        self.console.print(table)

        # Generate detailed JSON report
        detailed_report = {
            "summary": {
                "total_guides": len(results),
                "total_targets": len(self.targets),
                "target_type": self.target_type,
                "avg_conservation": np.mean([r["conservation"] for r in results]),
                "guides_with_forward_bias": sum(
                    1 for r in results if r["strand_bias"] == "forward"
                ),
                "guides_with_reverse_bias": sum(
                    1 for r in results if r["strand_bias"] == "reverse"
                ),
                "guides_with_seed_mismatches": sum(
                    1 for r in results if r["seed_mismatches"] > 0
                ),
                "avg_cfd_score": np.mean(
                    [r["avg_cfd_score"] for r in results if r["avg_cfd_score"] > 0]
                ),
            },
            "guides": results,
        }

        # Save report
        with open(output_file, "w") as f:
            json.dump(detailed_report, f, indent=2, default=str)

        self.console.print(f"\n[green]Enhanced report saved to:[/green] {output_file}")

        # Print key insights for cDNA
        if self.target_type == "cdna":
            self.console.print("\n[bold]cDNA-Specific Insights:[/bold]")
            forward_biased = sum(1 for r in results if r["strand_bias"] == "forward")
            reverse_biased = sum(1 for r in results if r["strand_bias"] == "reverse")

            self.console.print(
                f"• Guides targeting mRNA-sense strand: [cyan]{forward_biased}[/cyan]"
            )
            self.console.print(
                f"• Guides targeting antisense strand: [cyan]{reverse_biased}[/cyan]"
            )

            if forward_biased > reverse_biased:
                self.console.print(
                    "• [yellow]Majority of guides target the mRNA-sense strand[/yellow]"
                )
            elif reverse_biased > forward_biased:
                self.console.print(
                    "• [yellow]Majority of guides target the antisense strand[/yellow]"
                )

        return detailed_report


def main():
    """Main execution with enhanced options."""
    parser = argparse.ArgumentParser(
        description="Enhanced guide RNA validation with strand awareness"
    )
    parser.add_argument(
        "--guide-csv", type=Path, required=True, help="Path to guide RNA CSV file"
    )
    parser.add_argument(
        "--guide-fasta", type=Path, required=True, help="Path to guide RNA FASTA file"
    )
    parser.add_argument(
        "--target-fasta",
        type=Path,
        required=True,
        help="Path to target sequences FASTA file",
    )
    parser.add_argument(
        "--target-type",
        choices=["dna", "cdna"],
        default="dna",
        help="Type of target sequences (affects strand interpretation)",
    )
    parser.add_argument(
        "--output",
        type=Path,
        default=Path("enhanced_validation_report.json"),
        help="Output file for validation report",
    )

    args = parser.parse_args()

    # Validate input files
    for file_path in [args.guide_csv, args.guide_fasta, args.target_fasta]:
        if not file_path.exists():
            print(f"Error: File not found: {file_path}")
            return 1

    # Run enhanced validation
    validator = ImprovedGuideRNAValidator(
        args.guide_csv, args.guide_fasta, args.target_fasta, args.target_type
    )

    results = []
    for guide_id, guide_seq in track(
        validator.guides.items(),
        description="Validating guides with enhanced metrics...",
    ):
        result = validator.validate_guide_with_strand_awareness(guide_id, guide_seq)
        results.append(result)

    validator.generate_enhanced_report(results, args.output)

    return 0


if __name__ == "__main__":
    exit(main())
