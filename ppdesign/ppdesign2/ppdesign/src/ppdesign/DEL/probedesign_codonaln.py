import os
import shutil
import random
import subprocess
import sys
from Bio import SeqIO
import logging
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import typer
from typing import Optional
from Bio import AlignIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
from Bio.Align import MultipleSeqAlignment

app = typer.Typer()
# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def is_non_empty_dir(directory):
    """Check if a directory exists and is non-empty."""
    if os.path.exists(directory) and os.listdir(directory):
        return True
    return False


def create_codon_alignment_task(og_id, alignment_dir, conserved_fnn_dir, codon_dir):
    faa_alignment_file = os.path.join(alignment_dir, f"{og_id}.mafft")
    fnn_file = os.path.join(conserved_fnn_dir, f"{og_id}.fnn")
    output_file = os.path.join(codon_dir, f"{og_id}.codon")
    create_codon_alignment(faa_alignment_file, fnn_file, output_file)


def create_codon_alignments_for_orthogroups(
    orthogroups, alignment_dir, conserved_fnn_dir, codon_dir, threads
):
    with ThreadPoolExecutor(max_workers=threads) as executor:
        future_to_og = {
            executor.submit(
                create_codon_alignment_task,
                og_id,
                alignment_dir,
                conserved_fnn_dir,
                codon_dir,
            ): og_id
            for og_id in orthogroups
        }

        for future in tqdm(
            as_completed(future_to_og),
            total=len(orthogroups),
            desc="Creating Codon Alignments",
            unit="orthogroup",
        ):
            future.result()  # This will raise any exception encountered during the execution


def create_codon_alignment(faa_alignment_file, fnn_file, output_file):
    # Load the protein alignment
    prot_alignment = AlignIO.read(faa_alignment_file, "fasta")

    # Load the corresponding nucleotide sequences
    nucleotide_seqs = {
        record.id: record.seq for record in SeqIO.parse(fnn_file, "fasta")
    }

    # Create a new alignment for the nucleotide sequences
    codon_alignment = []

    for prot_record in prot_alignment:
        nuc_seq = nucleotide_seqs.get(prot_record.id)
        if nuc_seq:  # Ensure there's a corresponding nucleotide sequence
            aligned_nuc_seq = ""
            nuc_pos = 0

            for aa in prot_record.seq:
                if aa == "-":
                    aligned_nuc_seq += "---"
                else:
                    # Check if we have enough nucleotides remaining
                    if nuc_pos + 3 > len(nuc_seq):
                        print(
                            f"Warning: Nucleotide sequence for {prot_record.id} is shorter than expected. "
                            f"Expected at least {nuc_pos + 3} nucleotides, but only have {len(nuc_seq)}."
                        )
                        # Pad with N's if we run out of nucleotides
                        remaining = len(nuc_seq) - nuc_pos
                        if remaining > 0:
                            aligned_nuc_seq += str(nuc_seq[nuc_pos:]) + "N" * (
                                3 - remaining
                            )
                        else:
                            aligned_nuc_seq += "NNN"
                    else:
                        aligned_nuc_seq += str(nuc_seq[nuc_pos : nuc_pos + 3])
                    nuc_pos += 3

            # Create a new SeqRecord for the aligned nucleotide sequence
            codon_alignment.append(
                SeqRecord(Seq(aligned_nuc_seq), id=prot_record.id, description="")
            )

    # Write the codon alignment to a file
    AlignIO.write(MultipleSeqAlignment(codon_alignment), output_file, "fasta")


def align_faa_file(faa_file_path, alignment_dir):
    """
    Aligns a single .faa file using MAFFT and saves the output in the specified alignment directory.
    """
    base_name = os.path.basename(faa_file_path)
    alignment_file_name = base_name.replace(".faa", ".mafft")
    alignment_file_path = os.path.join(alignment_dir, alignment_file_name)

    # Command to run MAFFT
    mafft_cmd = ["mafft", "--auto", faa_file_path]

    # Run MAFFT and write output to the alignment file, suppress stdout and stderr
    with open(alignment_file_path, "w") as outfile, open(os.devnull, "w") as devnull:
        subprocess.run(mafft_cmd, stdout=outfile, stderr=devnull, check=True)


def align_all_faa_files(source_dir, alignment_dir, threads):
    """
    Aligns all .faa files in the source directory using MAFFT and multiprocessing,
    with a progress bar.
    """
    if not os.path.exists(alignment_dir):
        os.makedirs(alignment_dir)

    faa_files = [
        os.path.join(source_dir, f)
        for f in os.listdir(source_dir)
        if f.endswith(".faa")
    ]

    # Use ProcessPoolExecutor to parallelize alignment
    with ProcessPoolExecutor(max_workers=threads) as executor:
        # Map of future submissions
        future_to_file = {
            executor.submit(align_faa_file, faa_file, alignment_dir): faa_file
            for faa_file in faa_files
        }

        # Process futures as they complete
        for future in tqdm(
            as_completed(future_to_file),
            total=len(faa_files),
            desc="Aligning Files",
            unit="file",
        ):
            future.result()  # Raises exception of the function if there was one


def check_fna_processing_complete(genome_ids, faa_dir, fnn_dir):
    """
    Check if .faa and .fnn files have been generated for all genomes.
    """
    for genome_id in genome_ids:
        if not os.path.exists(
            os.path.join(faa_dir, f"{genome_id}.faa")
        ) or not os.path.exists(os.path.join(fnn_dir, f"{genome_id}.fnn")):
            return False
    return True


def copy_and_clean_faa_files(genome_ids, source_dir, target_dir):
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    for genome_id in genome_ids:
        source_file = os.path.join(source_dir, f"{genome_id}_protein.faa")
        target_file = os.path.join(target_dir, f"{genome_id}.faa")
        shutil.copy(source_file, target_file)
    clean_faa_files(target_dir)


def clean_faa_files(directory):
    """Remove asterisks from sequences and '_protein' from headers in .faa files."""
    for filename in os.listdir(directory):
        if filename.endswith(".faa"):
            filepath = os.path.join(directory, filename)
            cleaned_records = []
            for record in SeqIO.parse(filepath, "fasta"):
                record.seq = record.seq.strip("*")
                record.id = record.id.replace("_protein", "").split()[
                    0
                ]  # Remove '_protein' and everything after first whitespace
                record.description = ""  # Clear the description to avoid duplicate IDs
                cleaned_records.append(record)
            SeqIO.write(cleaned_records, filepath, "fasta")


def run_prodigal(fna_path, faa_dir, fnn_dir, base_name):
    faa_output = os.path.join(faa_dir, f"{base_name}.faa")
    fnn_output = os.path.join(fnn_dir, f"{base_name}.fnn")

    with open(os.devnull, "w") as devnull:
        subprocess.run(
            [
                "prodigal",
                "-i",
                fna_path,
                "-a",
                faa_output,
                "-d",
                fnn_output,
                "-p",
                "meta",
                "-q",
            ],
            stdout=devnull,
            stderr=subprocess.STDOUT,
        )

    # Clean and rename sequence headers in the output files
    for file_path in [faa_output, fnn_output]:
        with open(file_path, "r") as file:
            records = list(SeqIO.parse(file, "fasta"))
        for record in records:
            record.id = f"{base_name}|{record.id.split()[0]}"
            record.description = ""
        SeqIO.write(records, file_path, "fasta")


def process_fna_files(genome_ids, source_dir, faa_dir, fnn_dir, fna_dir, threads):
    """
    Process .fna files for each genome using Prodigal.
    """
    if not os.path.exists(fna_dir):
        os.makedirs(fna_dir)
    if not os.path.exists(faa_dir):
        os.makedirs(faa_dir)
    if not os.path.exists(fnn_dir):
        os.makedirs(fnn_dir)

    # Check if .faa and .fnn files have already been generated
    if check_fna_processing_complete(genome_ids, faa_dir, fnn_dir):
        logging.info(
            "All .faa and .fnn files have been generated. Skipping Prodigal processing."
        )
        return True

    def process_genome(genome_id):
        core_genome_id = genome_id.split("_")[-1]
        fna_files = [f for f in os.listdir(source_dir) if core_genome_id in f]
        if fna_files:
            matching_fna_file = fna_files[0]
            new_fna_name = f"{genome_id}.fna"
            new_fna_path = os.path.join(fna_dir, new_fna_name)
            shutil.copy(os.path.join(source_dir, matching_fna_file), new_fna_path)
            run_prodigal(new_fna_path, faa_dir, fnn_dir, genome_id)
        else:
            print(f"Warning: .fna file for {genome_id} not found.")
            return False
        return True

    # Use ThreadPoolExecutor to parallelize processing
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = list(
            tqdm(executor.map(process_genome, genome_ids), total=len(genome_ids))
        )
    return all(results)


def filter_orthogroups_by_conservation(
    orthogroups_file, conservation_level, total_genomes
):
    qualifying_orthogroups = []
    with open(orthogroups_file, "r") as infile:
        for i, line in enumerate(infile):
            parts = line.strip().split()
            orthogroup = parts[0]
            unique_genomes = set(part.split("|")[0] for part in parts[1:])
            if len(unique_genomes) >= round(conservation_level * total_genomes):
                qualifying_orthogroups.append(orthogroup)
    return qualifying_orthogroups


def create_multi_faa_fnn_files(
    qualifying_orthogroups,
    orthogroups_file,
    source_faa_dir,
    source_fnn_dir,
    target_faa_dir,
    target_fnn_dir,
    threads,
):
    """Create multi-FAA and multi-FNN files for conserved orthogroups using multithreading."""
    if not os.path.exists(target_faa_dir):
        os.makedirs(target_faa_dir)
    if not os.path.exists(target_fnn_dir):
        os.makedirs(target_fnn_dir)

    with open(orthogroups_file, "r") as infile:
        orthogroup_data = {line.split()[0]: line.strip().split()[1:] for line in infile}

    # Pre-load all needed genome files into dictionaries for efficient lookup
    print("Loading genome sequences into memory...")
    genome_faa_cache = {}
    genome_fnn_cache = {}

    # Get unique genome IDs from all orthogroups
    all_genome_ids = set()
    for og_name in qualifying_orthogroups:
        proteins = orthogroup_data.get(og_name, [])
        for protein_info in proteins:
            genome_id = protein_info.split("|", 1)[0]
            all_genome_ids.add(genome_id)

    # Load FAA files
    for genome_id in tqdm(all_genome_ids, desc="Loading FAA files"):
        faa_file_path = os.path.join(source_faa_dir, f"{genome_id}.faa")
        if os.path.exists(faa_file_path):
            genome_faa_cache[genome_id] = {}
            for record in SeqIO.parse(faa_file_path, "fasta"):
                genome_faa_cache[genome_id][record.id] = record

    # Load FNN files
    for genome_id in tqdm(all_genome_ids, desc="Loading FNN files"):
        fnn_file_path = os.path.join(source_fnn_dir, f"{genome_id}.fnn")
        if os.path.exists(fnn_file_path):
            genome_fnn_cache[genome_id] = {}
            for record in SeqIO.parse(fnn_file_path, "fasta"):
                # Store by the protein ID part for easier lookup
                if "|" in record.id:
                    protein_id = record.id.split("|", 1)[1]
                    genome_fnn_cache[genome_id][protein_id] = record

    def process_orthogroup(og_name):
        proteins = orthogroup_data.get(og_name, [])
        output_faa_path = os.path.join(target_faa_dir, f"{og_name}.faa")
        output_fnn_path = os.path.join(target_fnn_dir, f"{og_name}.fnn")

        with (
            open(output_faa_path, "w") as outfile_faa,
            open(output_fnn_path, "w") as outfile_fnn,
        ):
            for protein_info in proteins:
                genome_id, protein_id = protein_info.split("|", 1)

                # Lookup FAA record from cache
                if (
                    genome_id in genome_faa_cache
                    and protein_info in genome_faa_cache[genome_id]
                ):
                    SeqIO.write(
                        genome_faa_cache[genome_id][protein_info], outfile_faa, "fasta"
                    )

                # Lookup FNN record from cache
                if (
                    genome_id in genome_fnn_cache
                    and protein_id in genome_fnn_cache[genome_id]
                ):
                    SeqIO.write(
                        genome_fnn_cache[genome_id][protein_id], outfile_fnn, "fasta"
                    )

    # Use ThreadPoolExecutor to parallelize processing of each orthogroup
    print("Processing orthogroups...")
    with ThreadPoolExecutor(max_workers=threads) as executor:
        list(
            tqdm(
                executor.map(process_orthogroup, qualifying_orthogroups),
                total=len(qualifying_orthogroups),
            )
        )


def create_orthogroups_file(proteinortho_tsv, output_file):
    with open(proteinortho_tsv, "r") as infile, open(output_file, "w") as outfile:
        header = infile.readline().strip().split("\t")[3:]  # Skip initial columns
        for i, line in enumerate(infile):
            parts = line.strip().split("\t")
            orthogroup = f"og{i+1}"
            proteins = []
            for genome, protein_list in zip(header, parts[3:]):
                # Keep the .n suffix for the genome ID
                genome_id = genome.split("|")[0].replace(".faa", "")

                if protein_list and not protein_list.startswith("*"):
                    for protein in protein_list.split(","):
                        # Extract the correct protein ID
                        protein_id = protein.split("|")[-1]
                        proteins.append(f"{genome_id}|{protein_id}")
            if proteins:
                outfile.write(f"{orthogroup} {' '.join(proteins)}\n")


def move_and_rename_proteinortho_files(proteinortho_dir, level, group):
    # Ensure the target directory exists
    if not os.path.exists(proteinortho_dir):
        os.makedirs(proteinortho_dir)

    for file in os.listdir("."):
        if file.startswith(f"{level}__{group}_po") and not os.path.isdir(file):
            # Correctly update the new filename
            # If the filename starts with {level}__{group}_po twice, remove one occurrence
            if file.startswith(f"{level}__{group}_po{level}__{group}_po"):
                new_name = file.replace(f"{level}__{group}_po", "", 1)
            else:
                new_name = file

            src_path = os.path.join(".", file)
            dst_path = os.path.join(proteinortho_dir, new_name)

            print(f"Moving file from {src_path} to {dst_path}")  # Debugging print

            if os.path.exists(src_path):
                shutil.move(src_path, dst_path)
            else:
                print(f"Warning: File not found: {src_path}")


def read_taxonomy_file(filepath):
    taxonomy_dict = {}
    with open(filepath, "r") as file:
        for line in file:
            genome_id, taxonomy = line.strip().split("\t")
            taxonomy_dict[genome_id] = taxonomy
    return taxonomy_dict


def filter_genomes(taxonomy_dict, level, group):
    matched_genomes = []
    for genome_id, taxonomy in taxonomy_dict.items():
        taxonomy_levels = taxonomy.split(";")
        taxonomy_map = {
            t.split("__")[0]: t.split("__")[1].split()[0] for t in taxonomy_levels
        }
        if taxonomy_map.get(level[0], "").lower() == group.lower():
            matched_genomes.append(genome_id)
    return matched_genomes


def copy_genomes(genome_ids, source_dir, target_dir, input_type, faa_dir):
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)

    for genome_id in genome_ids:
        if input_type == "fna":
            # Use files generated by Prodigal
            source_file = os.path.join(faa_dir, f"{genome_id}.faa")
        elif input_type == "faa":
            # Copy from source_faa_dir, removing '_protein' suffix
            source_file = os.path.join(source_dir, f"{genome_id}_protein.faa")
            target_file = os.path.join(target_dir, f"{genome_id}.faa")
            shutil.copy(source_file, target_file)
        else:
            raise ValueError("Invalid input type. Use 'faa' or 'fna'.")

        if input_type == "faa":
            # Clean up the copied files only if '-i faa' is used
            clean_faa_files(target_dir)


@app.command()
def main(
    group: str = typer.Option(..., "-g", "--group", help="Taxonomic group to process"),
    level: str = typer.Option(..., "-l", "--level", help="Taxonomic level"),
    conservation_level: float = typer.Option(
        0.0, "-c", "--conservation", help="Conservation level for filtering orthogroups"
    ),
    sample_number: Optional[int] = typer.Option(
        None, "-n", "--number", help="Number of genomes to process"
    ),
    input_type: str = typer.Option(
        "faa", "-i", "--input", help="Input file type: 'faa' or 'fna'"
    ),
    threads: int = typer.Option(
        4, "-t", "--threads", help="Number of threads for processing"
    ),
):
    logging.basicConfig(level=logging.INFO)
    logging.info("Starting main process...")

    levels = {
        "domain": "d",
        "phylum": "p",
        "class": "c",
        "order": "o",
        "family": "f",
        "genus": "g",
        "species": "s",
    }

    logging.info(
        f"Level: {level}"
    )  # Debugging line to see what Typer passes as `level`
    if level not in levels:
        typer.echo(f"Invalid taxonomic level: {level}")
        raise typer.Exit(code=1)

    # Reading taxonomy file and filtering genomes
    logging.info("Reading taxonomy file...")
    try:
        taxonomy_dict = read_taxonomy_file("resources/bac120_taxonomy_reps.tsv")
        filtered_genomes = filter_genomes(taxonomy_dict, level, group)
        if not filtered_genomes:
            logging.warning(f"{level}__{group} not found")
            raise typer.Exit(code=1)
    except Exception as e:
        logging.error(f"Error reading taxonomy file: {e}")
        raise typer.Exit(code=1)

    if sample_number and len(filtered_genomes) > sample_number:
        logging.info(
            f"Randomly sampling {sample_number} genomes for orthogroup clustering"
        )
        filtered_genomes = random.sample(filtered_genomes, sample_number)

    # Process FNA/FAA files based on the input_type
    source_dir = "/global/cfs/cdirs/nelli/shared/03refs/gtdb/fna_reps"
    faa_dir, fnn_dir, fna_dir = (
        f"{level}__{group}_faa",
        f"{level}__{group}_fnn",
        f"{level}__{group}_genomes",
    )

    faa_dir = f"{level}__{group}_faa"

    try:
        if input_type == "fna":
            # Process FNA files, assuming process_fna_files function is defined and correct
            if not process_fna_files(
                filtered_genomes, source_dir, faa_dir, fnn_dir, fna_dir, threads
            ):
                logging.error("Error: .fna files processing failed.")
                raise typer.Exit(code=1)
        elif input_type == "faa":
            # Copy and clean FAA files, assuming copy_and_clean_faa_files function is defined and correct
            copy_and_clean_faa_files(filtered_genomes, source_dir, faa_dir)
        else:
            logging.error(f"Unsupported input type: {input_type}")
            raise typer.Exit(code=1)
    except Exception as e:
        logging.error(f"Error processing FNA/FAA files: {e}")
        sys.exit(1)

    # Additional logging for the number of genomes found
    logging.info(f"Found {len(filtered_genomes)} genomes matching {level}__{group}")

    target_dir = f"{level}__{group}_faa"
    if not is_non_empty_dir(target_dir):
        print("Copying genome files...")
        # Copying genome files
        try:
            copy_genomes(filtered_genomes, source_dir, target_dir, input_type, faa_dir)
        except Exception as e:
            logging.error(f"Error in copying genomes: {e}")
            sys.exit(1)
    else:
        logging.info(
            f"Skipping copy_genomes as {target_dir} already exists and is non-empty."
        )

    clean_faa_files(faa_dir)

    proteinortho_dir = f"{level}__{group}_po"
    if not is_non_empty_dir(proteinortho_dir):
        print("Running Proteinortho...")
        proteinortho_cmd = f"proteinortho -cpus={threads} -project={proteinortho_dir}/{level}__{group}_po {target_dir}/*.faa"
        # Running Proteinortho
        try:
            subprocess.run(proteinortho_cmd, shell=True, check=True)
        except subprocess.CalledProcessError as e:
            logging.error(f"Error running Proteinortho: {e}")
            sys.exit(1)
    else:
        logging.info(
            f"Skipping Proteinortho as {proteinortho_dir} already exists and is non-empty."
        )

    print("Moving and renaming Proteinortho output files...")
    try:
        move_and_rename_proteinortho_files(proteinortho_dir, level, group)
    except Exception as e:
        logging.error(f"Error moving/renaming Proteinortho files: {e}")
        sys.exit(1)

    proteinortho_tsv = os.path.join(
        proteinortho_dir, f"{level}__{group}_po.proteinortho.tsv"
    )
    if not os.path.exists(proteinortho_tsv):
        print(
            "Error: Proteinortho did not generate the expected output. Check for errors."
        )
        sys.exit(1)

    orthogroups_file = os.path.join(proteinortho_dir, f"{level}__{group}_og.groups")
    print("Creating orthogroups file...")
    # Creating orthogroups file
    try:
        create_orthogroups_file(proteinortho_tsv, orthogroups_file)
    except Exception as e:
        logging.error(f"Error creating orthogroups file: {e}")
        sys.exit(1)

    # Filter orthogroups by conservation
    try:
        qualifying_orthogroups = filter_orthogroups_by_conservation(
            orthogroups_file, conservation_level, len(filtered_genomes)
        )
    except Exception as e:
        logging.error(f"Error filtering orthogroups by conservation: {e}")
        sys.exit(1)

    if conservation_level > 0:
        conserved_og_percent = int(conservation_level * 100)
        conserved_faa_dir = f"{level}__{group}_og{conserved_og_percent}_faa"
        conserved_fnn_dir = f"{level}__{group}_og{conserved_og_percent}_fnn"
        if not is_non_empty_dir(conserved_faa_dir) or not is_non_empty_dir(
            conserved_fnn_dir
        ):
            print("Creating multi-FAA and FNN files for conserved orthogroups...")
            try:
                create_multi_faa_fnn_files(
                    qualifying_orthogroups,
                    orthogroups_file,
                    faa_dir,
                    fnn_dir,
                    conserved_faa_dir,
                    conserved_fnn_dir,
                    threads,
                )
            except Exception as e:
                logging.error(f"Error creating multi-FAA and FNN files: {e}")
                sys.exit(1)
        else:
            logging.info(
                "Skipping create_multi_faa_fnn_files as conserved directories already exist and are non-empty."
            )

        alignment_dir = f"{level}__{group}_og{conserved_og_percent}_aln"
        if not is_non_empty_dir(alignment_dir):
            try:
                align_all_faa_files(conserved_faa_dir, alignment_dir, threads)
            except Exception as e:
                logging.error(f"Error creating alignment: {e}")
                sys.exit(1)
        else:
            logging.info(
                f"Skipping align_all_faa_files as {alignment_dir} already exists and is non-empty."
            )

        codon_dir = f"{level}__{group}_og{conserved_og_percent}_codon"
        if not is_non_empty_dir(codon_dir):
            try:
                os.makedirs(codon_dir, exist_ok=True)
                create_codon_alignments_for_orthogroups(
                    qualifying_orthogroups,
                    alignment_dir,
                    conserved_fnn_dir,
                    codon_dir,
                    threads,
                )
            except Exception as e:
                logging.error(f"Error creating alignment: {e}")
                sys.exit(1)
        else:
            logging.info(
                f"Skipping codon alignment creation as {codon_dir} already exists and is non-empty."
            )


if __name__ == "__main__":
    app()
