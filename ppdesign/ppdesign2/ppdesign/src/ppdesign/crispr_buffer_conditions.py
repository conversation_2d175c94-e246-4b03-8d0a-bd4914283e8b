#!/usr/bin/env python3
"""
CRISPR-specific buffer conditions for thermodynamic calculations.

Standard CRISPR-Cas9 reaction conditions differ significantly from
typical oligonucleotide hybridization buffers.
"""

# Standard CRISPR-Cas9 in vitro cleavage buffer conditions
CRISPR_INVITRO_BUFFER = {
    "name": "Standard Cas9 Cleavage Buffer",
    "na_conc": 0.150,  # 150 mM NaCl (typical)
    "mg_conc": 0.010,  # 10 mM MgCl2 (required for Cas9 activity)
    "temperature": 37.0,  # 37°C standard
    "pH": 7.5,
    "notes": "Standard conditions for in vitro Cas9 cleavage assays",
}

# Cellular conditions (more relevant for in vivo applications)
CELLULAR_CONDITIONS = {
    "name": "Mammalian Cell Conditions",
    "na_conc": 0.140,  # ~140 mM Na+ in cytoplasm
    "k_conc": 0.140,  # ~140 mM K+ (dominant monovalent cation)
    "mg_conc": 0.0005,  # ~0.5 mM free Mg2+ in cells
    "temperature": 37.0,
    "pH": 7.2,
    "notes": "Physiological conditions in mammalian cells",
}

# SpCas9 RNP formation buffer (for guide loading)
RNP_FORMATION_BUFFER = {
    "name": "Cas9 RNP Formation Buffer",
    "na_conc": 0.020,  # 20 mM NaCl (low salt for RNP formation)
    "mg_conc": 0.0,  # No Mg2+ during RNP formation
    "temperature": 25.0,  # Room temperature for RNP assembly
    "pH": 7.5,
    "notes": "Conditions for Cas9-gRNA ribonucleoprotein complex formation",
}

# Cas9 storage buffer
CAS9_STORAGE_BUFFER = {
    "name": "Cas9 Storage Buffer",
    "na_conc": 0.300,  # 300 mM NaCl
    "mg_conc": 0.0,  # No MgCl2 in storage
    "temperature": -80.0,  # Storage temperature
    "glycerol": 0.5,  # 50% glycerol
    "pH": 7.5,
    "notes": "Long-term storage conditions for Cas9 protein",
}

# Nucleofection buffer (for cell delivery)
NUCLEOFECTION_BUFFER = {
    "name": "Nucleofection Buffer",
    "na_conc": 0.005,  # Very low salt (5 mM)
    "mg_conc": 0.0,  # No Mg2+
    "temperature": 25.0,  # Room temperature
    "pH": 7.2,
    "notes": "Low ionic strength for electroporation delivery",
}

# CRISPR screening conditions (lentiviral)
SCREENING_CONDITIONS = {
    "name": "Lentiviral CRISPR Screening",
    "na_conc": 0.140,  # Cellular conditions
    "k_conc": 0.140,
    "mg_conc": 0.0005,
    "temperature": 37.0,
    "pH": 7.2,
    "notes": "Conditions for genome-wide CRISPR screens in cells",
}


def get_crispr_conditions(application="in_vitro"):
    """
    Get appropriate buffer conditions for CRISPR applications.

    Args:
        application: Type of CRISPR application
            - "in_vitro": In vitro cleavage assays
            - "cellular": In vivo/cellular applications
            - "rnp_formation": Cas9-gRNA complex formation
            - "screening": Genome-wide screening

    Returns:
        Dictionary with buffer conditions
    """
    conditions_map = {
        "in_vitro": CRISPR_INVITRO_BUFFER,
        "cellular": CELLULAR_CONDITIONS,
        "rnp_formation": RNP_FORMATION_BUFFER,
        "screening": SCREENING_CONDITIONS,
        "storage": CAS9_STORAGE_BUFFER,
        "nucleofection": NUCLEOFECTION_BUFFER,
    }

    if application not in conditions_map:
        raise ValueError(
            f"Unknown application: {application}. Choose from {list(conditions_map.keys())}"
        )

    conditions = conditions_map[application].copy()

    # For thermodynamic calculations, we need to consider:
    # 1. The actual binding occurs in the Cas9 protein context, not free in solution
    # 2. The PAM interaction and protein-DNA contacts stabilize the complex
    # 3. The guide RNA is pre-organized in the Cas9 structure

    # Adjust for protein-mediated binding (these are approximations)
    if application in ["in_vitro", "cellular", "screening"]:
        # The Cas9 protein provides additional stabilization
        # This is a rough approximation - actual protein effects are complex
        conditions["protein_stabilization"] = -5.0  # kcal/mol additional stability
        conditions["pam_requirement"] = True  # PAM is required for binding

    return conditions


# Important notes for gRNA thermodynamics:
THERMODYNAMIC_NOTES = """
IMPORTANT CONSIDERATIONS FOR CRISPR GUIDE RNA THERMODYNAMICS:

1. PROTEIN CONTEXT:
   - Guide RNA binding occurs within the Cas9 protein, not free in solution
   - The protein pre-organizes the guide RNA in an A-form helix
   - Protein-DNA contacts contribute significantly to binding energy
   - PAM recognition is required for stable binding

2. BUFFER CONDITIONS:
   - In vitro Cas9 assays typically use:
     * 100-150 mM NaCl or KCl
     * 5-10 mM MgCl2 (essential for nuclease activity)
     * pH 7.5-8.0
     * 37°C
   
   - Cellular conditions:
     * ~140 mM K+ (primary monovalent cation)
     * ~10 mM Na+
     * ~0.5 mM free Mg2+ (most is chelated)
     * pH ~7.2
     * 37°C

3. KINETIC CONSIDERATIONS:
   - Cas9 binding is essentially irreversible once formed
   - Off-rates are very slow (hours to days)
   - On-rates are diffusion-limited for PAM-containing targets
   - Mismatches primarily affect off-rates, not on-rates

4. MISMATCH TOLERANCE:
   - PAM-proximal region (positions 1-10): Critical for binding
   - PAM-distal region (positions 11-20): More mismatch-tolerant
   - Seed region (positions 1-8): Most critical for specificity

5. THERMODYNAMIC MODELS:
   - Standard DNA-RNA hybrid models don't fully capture:
     * Protein-mediated stabilization
     * PAM requirement
     * Conformational constraints
     * R-loop formation energy
   
   - Empirical corrections needed for:
     * Cas9 binding energy contribution (~-10 to -15 kcal/mol)
     * PAM interaction energy (~-5 kcal/mol)
     * R-loop formation penalty (~+3 kcal/mol)

RECOMMENDATIONS:
- Use 150 mM NaCl, 10 mM MgCl2 for in vitro predictions
- Use 140 mM KCl, 0.5 mM MgCl2 for cellular predictions
- Apply protein stabilization correction factors
- Consider PAM-proximal weighting for mismatch penalties
- Validate predictions with experimental cleavage data
"""


def print_buffer_comparison():
    """Print a comparison of different CRISPR buffer conditions."""
    print("CRISPR-Cas9 Buffer Conditions Comparison")
    print("=" * 60)

    applications = ["in_vitro", "cellular", "rnp_formation", "nucleofection"]

    for app in applications:
        conditions = get_crispr_conditions(app)
        print(f"\n{conditions['name']}:")
        print(f"  Na+ concentration: {conditions.get('na_conc', 0)*1000:.0f} mM")
        if "k_conc" in conditions:
            print(f"  K+ concentration: {conditions.get('k_conc', 0)*1000:.0f} mM")
        print(f"  Mg2+ concentration: {conditions.get('mg_conc', 0)*1000:.1f} mM")
        print(f"  Temperature: {conditions['temperature']:.0f}°C")
        print(f"  pH: {conditions['pH']}")
        print(f"  Notes: {conditions['notes']}")

    print("\n" + "=" * 60)
    print("For thermodynamic calculations, use:")
    print("  - In vitro assays: 150 mM NaCl, 10 mM MgCl2, 37°C")
    print("  - Cellular predictions: 140 mM KCl, 0.5 mM free Mg2+, 37°C")
    print("  - Consider protein stabilization effects (~-10 kcal/mol)")


if __name__ == "__main__":
    print(THERMODYNAMIC_NOTES)
    print("\n")
    print_buffer_comparison()
