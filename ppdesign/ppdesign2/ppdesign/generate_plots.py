#!/usr/bin/env python3
"""Generate static plots from guide RNA validation results."""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Set style
plt.style.use("seaborn-v0_8-darkgrid")
sns.set_palette("husl")

# Create output directory
output_dir = Path("results/visualization_plots")
output_dir.mkdir(exist_ok=True, parents=True)

print("Loading data...")
# Load validation data
with open("results/validation_fixed_final.json", "r") as f:
    validation_data = json.load(f)

# Process into DataFrame
guides_data = []
for guide in validation_data["guides"]:
    guide_info = {
        "guide_id": guide["guide_id"],
        "sequence": guide["sequence"][:10] + "...",
        "hits": guide["validation"]["hits"],
        "conservation": guide["validation"]["conservation"],
        "avg_identity": guide["validation"]["avg_identity"],
        "perfect_matches": guide["validation"]["perfect_matches"],
        "central_mismatches": guide["validation"]["central_mismatches"],
        "peripheral_mismatches": guide["validation"]["peripheral_mismatches"],
    }
    guides_data.append(guide_info)

guides_df = pd.DataFrame(guides_data)

# Calculate quality score
guides_df["quality_score"] = (
    (guides_df["conservation"] / 100) * 0.3
    + (guides_df["avg_identity"] / 100) * 0.4
    + (guides_df["perfect_matches"] / guides_df["hits"]) * 0.3
) * 100

print(f"Generating visualizations for {len(guides_df)} guides...")

# 1. Performance Overview Heatmap
fig, ax = plt.subplots(figsize=(12, 8))
metrics = guides_df.set_index("guide_id")[
    ["conservation", "avg_identity", "quality_score"]
]
sns.heatmap(
    metrics.T, annot=True, fmt=".1f", cmap="RdYlGn", ax=ax, cbar_kws={"label": "Score"}
)
ax.set_title("Guide RNA Performance Metrics", fontsize=16, fontweight="bold")
plt.tight_layout()
plt.savefig(output_dir / "performance_heatmap.png", dpi=300, bbox_inches="tight")
plt.close()
print("✓ Saved performance_heatmap.png")

# 2. Conservation vs Identity Scatter
fig, ax = plt.subplots(figsize=(10, 8))
scatter = ax.scatter(
    guides_df["conservation"],
    guides_df["avg_identity"],
    s=guides_df["hits"] * 5,
    c=guides_df["quality_score"],
    cmap="viridis",
    alpha=0.7,
    edgecolors="black",
)
ax.set_xlabel("Conservation (%)", fontsize=14)
ax.set_ylabel("Average Identity (%)", fontsize=14)
ax.set_title("Conservation vs Identity Analysis", fontsize=16, fontweight="bold")
plt.colorbar(scatter, label="Quality Score")

# Add labels
for idx, row in guides_df.iterrows():
    ax.annotate(
        row["guide_id"],
        (row["conservation"], row["avg_identity"]),
        xytext=(5, 5),
        textcoords="offset points",
        fontsize=8,
    )

plt.tight_layout()
plt.savefig(output_dir / "conservation_vs_identity.png", dpi=300, bbox_inches="tight")
plt.close()
print("✓ Saved conservation_vs_identity.png")

# 3. Mismatch Analysis
fig, axes = plt.subplots(1, 2, figsize=(14, 6))

# Central vs peripheral
mismatch_data = guides_df[["guide_id", "central_mismatches", "peripheral_mismatches"]]
mismatch_data.set_index("guide_id").plot(
    kind="bar", ax=axes[0], color=["#FF6B6B", "#4ECDC4"]
)
axes[0].set_title("Mismatch Distribution by Guide RNA", fontsize=14)
axes[0].set_xlabel("Guide RNA")
axes[0].set_ylabel("Mismatch Count")
axes[0].legend(["Central", "Peripheral"])
axes[0].tick_params(axis="x", rotation=45)

# Perfect matches ratio
guides_df["perfect_ratio"] = (guides_df["perfect_matches"] / guides_df["hits"]) * 100
axes[1].bar(
    range(len(guides_df)),
    guides_df["perfect_ratio"].values,
    color=plt.cm.RdYlGn(guides_df["perfect_ratio"] / 100),
)
axes[1].set_xticks(range(len(guides_df)))
axes[1].set_xticklabels(guides_df["guide_id"], rotation=45)
axes[1].set_ylabel("Perfect Match Ratio (%)")
axes[1].set_title("Perfect Match Percentage", fontsize=14)

plt.tight_layout()
plt.savefig(output_dir / "mismatch_analysis.png", dpi=300, bbox_inches="tight")
plt.close()
print("✓ Saved mismatch_analysis.png")

# 4. Quality Ranking
fig, ax = plt.subplots(figsize=(10, 8))
quality_ranked = guides_df.sort_values("quality_score", ascending=True)
bars = ax.barh(
    range(len(quality_ranked)),
    quality_ranked["quality_score"],
    color=plt.cm.RdYlGn(quality_ranked["quality_score"] / 100),
)
ax.set_yticks(range(len(quality_ranked)))
ax.set_yticklabels(quality_ranked["guide_id"])
ax.set_xlabel("Quality Score")
ax.set_title("Guide RNA Quality Ranking", fontsize=16, fontweight="bold")

# Add values
for i, v in enumerate(quality_ranked["quality_score"]):
    ax.text(v + 0.5, i, f"{v:.1f}", va="center")

plt.tight_layout()
plt.savefig(output_dir / "quality_ranking.png", dpi=300, bbox_inches="tight")
plt.close()
print("✓ Saved quality_ranking.png")

# 5. Summary Statistics
fig, ax = plt.subplots(figsize=(10, 6))
ax.axis("off")

# Create summary table
summary_stats = {
    "Metric": [
        "Total Guides",
        "Avg Conservation",
        "Avg Identity",
        "Avg Quality Score",
        "Guides with Perfect Matches",
        "Avg Central Mismatches",
    ],
    "Value": [
        len(guides_df),
        f"{guides_df['conservation'].mean():.1f}%",
        f"{guides_df['avg_identity'].mean():.1f}%",
        f"{guides_df['quality_score'].mean():.1f}",
        f"{(guides_df['perfect_matches'] > 0).sum()}/{len(guides_df)}",
        f"{guides_df['central_mismatches'].mean():.1f}",
    ],
}

table = ax.table(
    cellText=list(zip(summary_stats["Metric"], summary_stats["Value"])),
    colLabels=["Metric", "Value"],
    cellLoc="left",
    loc="center",
    colWidths=[0.6, 0.3],
)
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 2)

ax.set_title("Summary Statistics", fontsize=16, fontweight="bold", pad=20)
plt.savefig(output_dir / "summary_stats.png", dpi=300, bbox_inches="tight")
plt.close()
print("✓ Saved summary_stats.png")

print(f"\n✅ All visualizations saved to {output_dir}/")
print("\nTop 3 Guide RNAs by Quality Score:")
for idx, row in guides_df.nlargest(3, "quality_score").iterrows():
    print(
        f"  {row['guide_id']}: {row['quality_score']:.1f} (Conservation: {row['conservation']:.0f}%, Identity: {row['avg_identity']:.1f}%)"
    )
