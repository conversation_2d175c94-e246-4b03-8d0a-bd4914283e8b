#!/usr/bin/env python3
"""Visualize gRNA matches in multiple sequence alignment with highlighted regions."""

import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from Bio import SeqIO
from Bio.Seq import Seq
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path


def find_grna_in_sequence(grna_seq, target_seq):
    """Find gRNA positions in target sequence (both strands)."""
    positions = []
    target_seq = target_seq.upper()
    grna_seq = grna_seq.upper()

    # Check forward strand
    start = 0
    while True:
        pos = target_seq.find(grna_seq, start)
        if pos == -1:
            break
        # Check for PAM
        if pos + len(grna_seq) + 2 <= len(target_seq):
            pam = target_seq[pos + len(grna_seq) : pos + len(grna_seq) + 3]
            if len(pam) >= 2 and pam[1:3] in ["GG", "AG"]:
                positions.append((pos, pos + len(grna_seq), "+", pam))
        start = pos + 1

    # Check reverse strand
    rev_grna = str(Seq(grna_seq).reverse_complement())
    start = 0
    while True:
        pos = target_seq.find(rev_grna, start)
        if pos == -1:
            break
        # Check for PAM (CCN or CTN on reverse)
        if pos >= 3:
            pam = target_seq[pos - 3 : pos]
            if len(pam) >= 2 and pam[0:2] in ["CC", "CT"]:
                positions.append((pos, pos + len(grna_seq), "-", pam))
        start = pos + 1

    return positions


def extract_alignment_regions(sequences, grna_positions, window=50):
    """Extract regions around gRNA matches for alignment visualization."""
    aligned_regions = []

    for seq_id, seq in sequences.items():
        if seq_id in grna_positions and grna_positions[seq_id]:
            for start, end, strand, pam in grna_positions[seq_id]:
                # Extend window around gRNA
                region_start = max(0, start - window)
                region_end = min(len(seq), end + window + 3)  # Include PAM

                region_seq = seq[region_start:region_end]
                grna_start_in_region = start - region_start
                grna_end_in_region = end - region_start

                aligned_regions.append(
                    {
                        "seq_id": seq_id[:30] + "..." if len(seq_id) > 30 else seq_id,
                        "sequence": region_seq,
                        "grna_start": grna_start_in_region,
                        "grna_end": grna_end_in_region,
                        "strand": strand,
                        "pam": pam,
                        "original_pos": start,
                    }
                )

    return aligned_regions


def visualize_grna_alignment(
    aligned_regions, grna_seq, output_file, title="gRNA Alignment Visualization"
):
    """Create visualization of gRNA alignment with highlighted regions."""
    if not aligned_regions:
        print("No alignment regions to visualize")
        return

    # Prepare figure
    fig, ax = plt.subplots(figsize=(16, len(aligned_regions) * 0.4 + 2))

    # Color scheme
    colors = {
        "A": "#5E5E5E",  # Dark gray
        "T": "#5E5E5E",  # Dark gray
        "G": "#5E5E5E",  # Dark gray
        "C": "#5E5E5E",  # Dark gray
        "N": "#CCCCCC",  # Light gray
        "-": "#FFFFFF",  # White
    }

    grna_color = "#FF6B6B"  # Red for gRNA
    pam_color = "#4ECDC4"  # Teal for PAM

    # Plot each sequence
    y_pos = 0
    max_len = max(len(r["sequence"]) for r in aligned_regions)

    for region in aligned_regions:
        seq = region["sequence"]
        grna_start = region["grna_start"]
        grna_end = region["grna_end"]

        # Draw sequence background
        for i, nt in enumerate(seq):
            color = colors.get(nt.upper(), colors["N"])
            rect = patches.Rectangle(
                (i, y_pos), 1, 0.8, linewidth=0.5, edgecolor="white", facecolor=color
            )
            ax.add_patch(rect)

        # Highlight gRNA region
        rect = patches.Rectangle(
            (grna_start, y_pos),
            grna_end - grna_start,
            0.8,
            linewidth=2,
            edgecolor="darkred",
            facecolor=grna_color,
            alpha=0.7,
        )
        ax.add_patch(rect)

        # Highlight PAM
        if region["strand"] == "+":
            pam_start = grna_end
            if pam_start + 3 <= len(seq):
                rect = patches.Rectangle(
                    (pam_start, y_pos),
                    3,
                    0.8,
                    linewidth=2,
                    edgecolor="darkgreen",
                    facecolor=pam_color,
                    alpha=0.7,
                )
                ax.add_patch(rect)
        else:
            pam_start = grna_start - 3
            if pam_start >= 0:
                rect = patches.Rectangle(
                    (pam_start, y_pos),
                    3,
                    0.8,
                    linewidth=2,
                    edgecolor="darkgreen",
                    facecolor=pam_color,
                    alpha=0.7,
                )
                ax.add_patch(rect)

        # Add sequence label
        label = f"{region['seq_id']} ({region['strand']}) pos:{region['original_pos']}"
        ax.text(-1, y_pos + 0.4, label, ha="right", va="center", fontsize=8)

        y_pos += 1

    # Add gRNA sequence at top
    ax.text(
        max_len / 2,
        -1.5,
        f"gRNA: {grna_seq}",
        ha="center",
        va="center",
        fontsize=10,
        fontweight="bold",
        color=grna_color,
    )

    # Set plot properties
    ax.set_xlim(-max_len * 0.3, max_len)
    ax.set_ylim(-2, y_pos)
    ax.set_aspect("equal")
    ax.axis("off")
    ax.set_title(title, fontsize=14, fontweight="bold", pad=20)

    # Add legend
    legend_elements = [
        patches.Patch(
            facecolor=grna_color, edgecolor="darkred", alpha=0.7, label="gRNA sequence"
        ),
        patches.Patch(
            facecolor=pam_color, edgecolor="darkgreen", alpha=0.7, label="PAM site"
        ),
        patches.Patch(
            facecolor="#5E5E5E", edgecolor="white", label="Flanking sequence"
        ),
    ]
    ax.legend(handles=legend_elements, loc="upper right", bbox_to_anchor=(1.0, 1.0))

    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches="tight")
    plt.close()

    print(f"Visualization saved to {output_file}")


def create_text_alignment(aligned_regions, grna_seq, output_file):
    """Create text-based alignment visualization."""
    with open(output_file, "w") as f:
        f.write("=" * 100 + "\n")
        f.write("gRNA SEQUENCE ALIGNMENT VISUALIZATION\n")
        f.write(f"gRNA: {grna_seq}\n")
        f.write("=" * 100 + "\n\n")

        for region in aligned_regions:
            seq = region["sequence"]
            grna_start = region["grna_start"]
            grna_end = region["grna_end"]

            # Create visual representation
            visual = [" "] * len(seq)

            # Mark gRNA region
            for i in range(grna_start, grna_end):
                visual[i] = "█"

            # Mark PAM
            if region["strand"] == "+":
                for i in range(grna_end, min(grna_end + 3, len(seq))):
                    visual[i] = "▓"
            else:
                for i in range(max(0, grna_start - 3), grna_start):
                    visual[i] = "▓"

            # Write to file
            f.write(
                f">{region['seq_id']} strand:{region['strand']} pos:{region['original_pos']} PAM:{region['pam']}\n"
            )

            # Write sequence in chunks of 60
            for i in range(0, len(seq), 60):
                chunk_seq = seq[i : i + 60]
                chunk_visual = "".join(visual[i : i + 60])

                f.write(f"Seq: {chunk_seq}\n")
                f.write(f"gRNA: {chunk_visual}\n")
                if i + 60 < len(seq):
                    f.write("\n")

            f.write("\n" + "-" * 80 + "\n\n")

        f.write("\nLEGEND:\n")
        f.write("█ = gRNA sequence\n")
        f.write("▓ = PAM site\n")
        f.write("  = Flanking sequence\n")


def main():
    # Load sequences
    test_fasta = "tests/test.fna"
    sequences = {}
    for record in SeqIO.parse(test_fasta, "fasta"):
        sequences[record.id] = str(record.seq)

    # Load top gRNAs
    csv_file = "results/results/validation_run/guide_rnas.csv"
    df = pd.read_csv(csv_file)

    # Create output directory
    output_dir = Path("results/grna_msa_visualization")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Analyze top 3 gRNAs for visualization
    for idx in range(min(3, len(df))):
        row = df.iloc[idx]
        grna_id = row["Guide_ID"]
        grna_seq = row["Sequence"]
        conservation = row["Conservation"]
        target_sequences = row["Target_Sequences"].split(";")

        print(f"\nProcessing {grna_id}: {grna_seq}")
        print(f"Conservation: {conservation}")

        # Find gRNA positions in all target sequences
        grna_positions = {}
        for target_id in target_sequences:
            if target_id in sequences:
                positions = find_grna_in_sequence(grna_seq, sequences[target_id])
                if positions:
                    grna_positions[target_id] = positions

        # Extract alignment regions
        aligned_regions = extract_alignment_regions(
            sequences, grna_positions, window=30
        )

        # Create visualizations
        if aligned_regions:
            # Limit to first 20 matches for clarity
            aligned_regions = aligned_regions[:20]

            # Create graphical visualization
            output_image = output_dir / f"{grna_id}_alignment.png"
            visualize_grna_alignment(
                aligned_regions,
                grna_seq,
                output_image,
                title=f"{grna_id} - {grna_seq} (Conservation: {conservation})",
            )

            # Create text alignment
            output_text = output_dir / f"{grna_id}_alignment.txt"
            create_text_alignment(aligned_regions, grna_seq, output_text)

            print(f"✓ Created visualization: {output_image}")
            print(f"✓ Created text alignment: {output_text}")

    # Create summary HTML file
    html_file = output_dir / "alignment_summary.html"
    with open(html_file, "w") as f:
        f.write(
            """
<!DOCTYPE html>
<html>
<head>
    <title>gRNA MSA Visualization</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .grna-section { margin-bottom: 40px; border-bottom: 2px solid #ccc; padding-bottom: 20px; }
        img { max-width: 100%; border: 1px solid #ddd; margin: 10px 0; }
        .sequence { font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .legend { background: #e8f4f3; padding: 10px; border-left: 4px solid #4ECDC4; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>gRNA Multiple Sequence Alignment Visualization</h1>
    <div class="legend">
        <h3>Legend</h3>
        <p><span style="background:#FF6B6B; padding:2px 8px;">Red blocks</span> = gRNA sequence (20bp)</p>
        <p><span style="background:#4ECDC4; padding:2px 8px;">Teal blocks</span> = PAM site (NGG/NAG)</p>
        <p><span style="background:#5E5E5E; color:white; padding:2px 8px;">Gray blocks</span> = Flanking sequences</p>
    </div>
"""
        )

        for idx in range(min(3, len(df))):
            row = df.iloc[idx]
            grna_id = row["Guide_ID"]
            grna_seq = row["Sequence"]
            conservation = row["Conservation"]

            f.write(
                f"""
    <div class="grna-section">
        <h2>{grna_id}</h2>
        <p class="sequence">Sequence: {grna_seq}</p>
        <p>Conservation: {conservation}</p>
        <p>PAM: {row['PAM']}</p>
        <img src="{grna_id}_alignment.png" alt="{grna_id} alignment">
        <p><a href="{grna_id}_alignment.txt">View text alignment</a></p>
    </div>
"""
            )

        f.write(
            """
</body>
</html>
"""
        )

    print(f"\n📊 Summary HTML created: {html_file}")
    print("\n✅ MSA visualization complete!")


if __name__ == "__main__":
    main()
