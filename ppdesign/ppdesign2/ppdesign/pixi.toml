[project]
name = "ppdesign"
version = "0.2.0"
description = "Modern bioinformatics pipeline for oligonucleotide probe and primer design"
authors = ["PPDesign Development Team"]
channels = ["conda-forge", "bioconda"]
platforms = ["linux-64", "osx-64", "osx-arm64"]

[tasks]
# Main pipeline tasks
design-unified = { cmd = "python -m ppdesign.probedesign_unified", env = { PYTHONPATH = "src" }, description = "Gene-based probe design using codon alignments" }
design-nucleotide = { cmd = "python -m ppdesign.probedesign_nucleotide", env = { PYTHONPATH = "src" }, description = "Direct nucleotide probe design using k-mer/MSA analysis" }
select-oligos = { cmd = "python -m ppdesign.probedesign_seqselection", env = { PYTHONPATH = "src" }, description = "Select optimal oligonucleotides from candidates" }
rank-probes = { cmd = "python -m ppdesign.probedesign_rank", env = { PYTHONPATH = "src" }, description = "Rank probes by quality metrics" }
count-kmers = { cmd = "python -m ppdesign.kmer_finder", env = { PYTHONPATH = "src" }, description = "Count k-mer occurrences in sequences" }

# CRISPR guide RNA design
ppdesign-grna = { cmd = "python -m ppdesign.probedesign_grna", env = { PYTHONPATH = "src" }, description = "Design CRISPR guide RNAs with SpCas9 PAM sites" }
validate-grna = { cmd = "python -m ppdesign.validate_grna_alignments", env = { PYTHONPATH = "src" }, description = "Validate gRNA alignments against targets" }
validate-grna-cdna = { cmd = "python -m ppdesign.validate_grna_cdna", env = { PYTHONPATH = "src" }, description = "Validate gRNAs for cDNA applications" }
validate-grna-thermo = { cmd = "python -m ppdesign.validate_grna_thermo", env = { PYTHONPATH = "src" }, description = "Thermodynamic analysis of gRNA binding" }

# Jupyter tasks
jupyter-tailscale = { cmd = "./start_jupyter_tailscale.sh", env = { PYTHONPATH = "src" } }
jupyter-local = { cmd = "jupyter lab --no-browser", env = { PYTHONPATH = "src" } }

# Development tasks
test = { cmd = "python -m pytest tests/ -v", env = { PYTHONPATH = "src" } }
test-cov = "pytest tests/ --cov=ppdesign --cov-report=html"
lint = "ruff check src/"
format = "ruff format src/"
type-check = "mypy src/"
docs = "mkdocs serve"
build-docs = "mkdocs build"

# Terminal MSA viewer
msa-view = { cmd = "python src/ppdesign/terminal_msa_viewer.py view" }
msa-compare = { cmd = "python src/ppdesign/terminal_msa_viewer.py compare" }
msa-stats = { cmd = "python src/ppdesign/terminal_msa_viewer.py stats" }
msa-align = { cmd = "python src/ppdesign/terminal_msa_detailed.py align" }
msa-consensus = { cmd = "python src/ppdesign/terminal_msa_detailed.py consensus" }
msa-mismatch = { cmd = "python src/ppdesign/terminal_msa_mismatch.py" }

# gRNA alignment viewer
grna-align = { cmd = "python src/ppdesign/grna_alignment_viewer.py" }

# Utility tasks
clean = "rm -rf build/ dist/ *.egg-info/ .coverage htmlcov/ .pytest_cache/ .mypy_cache/ .ruff_cache/"
install-dev = "pip install -e ."

[dependencies]
# Core scientific computing
python = ">=3.11,<3.13"
numpy = ">=1.26"
pandas = ">=2.2"
polars = ">=0.20"
biopython = ">=1.83"
duckdb = ">=0.10"

# Bioinformatics tools
blast = ">=2.15"
diamond = ">=2.1"
mafft = ">=7.520"
muscle = ">=5.1"
minimap2 = ">=2.26"
proteinortho = ">=6.3"
prodigal-gv = ">=2.11"
cd-hit = ">=4.8"
mmseqs2 = ">=15.0"
parasail-python = ">=1.3"

# Visualization
matplotlib = ">=3.8"
seaborn = ">=0.13"
plotly = ">=5.18"
jupyterlab = ">=4.0"
ipywidgets = ">=8.1"
bokeh = ">=3.3"
scikit-learn = ">=1.3"

# CLI and utilities
typer = ">=0.9"
tqdm = ">=4.66"
click = ">=8.1"
rich = ">=13.7"
requests = ">=2.31"

# Development dependencies
[feature.dev.dependencies]
pytest = ">=7.4"
pytest-cov = ">=4.1"
ruff = ">=0.3"
mypy = ">=1.9"
black = ">=24.2"
pre-commit = ">=3.6"
mkdocs = ">=1.5"
mkdocs-material = ">=9.5"
mkdocstrings = ">=0.24"
mkdocstrings-python = ">=1.8"

[feature.dev.tasks]
pre-commit-install = "pre-commit install"
pre-commit = "pre-commit run --all-files"

[environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }

# [activation]
# scripts = ["activate.sh"]

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "ARG",  # flake8-unused-arguments
    "SIM",  # flake8-simplify
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "B008",  # do not perform function calls in argument defaults
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
pythonpath = ["src"]

[tool.coverage.run]
source = ["src/ppdesign"]
omit = ["*/tests/*", "*/test_*.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if __name__ == .__main__.:",
    "raise AssertionError",
    "raise NotImplementedError",
]