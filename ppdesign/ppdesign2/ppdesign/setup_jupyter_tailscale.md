# Setting up Jupyter Lab for Tailscale Network Access

## Option 1: Direct Jupyter Lab Configuration (Recommended)

### Step 1: Generate Jupyter Configuration
```bash
jupyter lab --generate-config
```

### Step 2: Create Password Hash
```python
# In Python, generate a password hash
from jupyter_server.auth import passwd
passwd()
# Enter your password when prompted, copy the hash
```

### Step 3: Configure Jupyter for Network Access

Edit `~/.jupyter/jupyter_lab_config.py`:

```python
# ~/.jupyter/jupyter_lab_config.py

# Allow access from any IP (Tailscale will handle security)
c.ServerApp.ip = '0.0.0.0'

# Or bind specifically to your Tailscale IP (more secure)
# Get your Tailscale IP with: tailscale ip -4
# c.ServerApp.ip = '100.x.x.x'  # Your Tailscale IP

# Set a specific port
c.ServerApp.port = 8888

# Disable browser auto-opening
c.ServerApp.open_browser = False

# Set password (use the hash from step 2)
c.ServerApp.password = 'argon2:...'  # Your password hash

# Allow remote access
c.ServerApp.allow_remote_access = True

# Optional: Use SSL (recommended for production)
# c.ServerApp.certfile = '/path/to/mycert.pem'
# c.ServerApp.keyfile = '/path/to/mykey.key'

# Optional: Set a base URL if using reverse proxy
# c.ServerApp.base_url = '/jupyter'

# Disable token-based authentication (using password instead)
c.ServerApp.token = ''

# Allow embedding in iframes (if needed)
c.ServerApp.tornado_settings = {
    'headers': {
        'Content-Security-Policy': "frame-ancestors 'self' https://*.tailscale.com"
    }
}
```

### Step 4: Create Startup Script

```bash
#!/bin/bash
# ~/start_jupyter_tailscale.sh

# Get Tailscale IP
TAILSCALE_IP=$(tailscale ip -4)

echo "Starting Jupyter Lab on Tailscale IP: $TAILSCALE_IP"
echo "Access at: http://$TAILSCALE_IP:8888"

# Start Jupyter Lab with specific config
jupyter lab \
    --ip=$TAILSCALE_IP \
    --port=8888 \
    --no-browser \
    --NotebookApp.allow_origin='*' \
    --NotebookApp.allow_remote_access=True
```

Make it executable:
```bash
chmod +x ~/start_jupyter_tailscale.sh
```

## Option 2: Using Tailscale Serve (Beta Feature)

If you have Tailscale Serve enabled:

```bash
# Start Jupyter locally
jupyter lab --port=8888 --no-browser

# Expose via Tailscale Serve
tailscale serve https / http://localhost:8888

# Or for HTTP only
tailscale serve http:8888 / http://localhost:8888
```

## Option 3: Using Tailscale Funnel (Public Access)

⚠️ **Warning**: This makes your Jupyter publicly accessible on the internet!

```bash
# Only use with strong authentication!
tailscale funnel 8888
```

## Option 4: Docker Container with Tailscale

Create a `docker-compose.yml`:

```yaml
version: '3.8'

services:
  jupyter:
    image: jupyter/scipy-notebook:latest
    container_name: ppdesign-jupyter
    ports:
      - "0.0.0.0:8888:8888"  # Bind to all interfaces
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN}
    volumes:
      - ./notebooks:/home/<USER>/notebooks
      - ./src:/home/<USER>/src
      - ./results:/home/<USER>/results
      - ./tests:/home/<USER>/tests
    working_dir: /home/<USER>
    command: >
      start-notebook.sh
      --NotebookApp.token='${JUPYTER_TOKEN}'
      --NotebookApp.allow_origin='*'
      --NotebookApp.ip='0.0.0.0'
```

Run with:
```bash
JUPYTER_TOKEN=your-secure-token docker-compose up -d
```

## Option 5: SystemD Service (Production)

Create `/etc/systemd/system/jupyter-lab.service`:

```ini
[Unit]
Description=Jupyter Lab Server
After=network.target

[Service]
Type=simple
User=fschulz
Group=fschulz
WorkingDirectory=/home/<USER>/dev/ppdesign
Environment="PATH=/home/<USER>/.pixi/envs/ppdesign/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
ExecStart=/home/<USER>/.pixi/envs/ppdesign/bin/jupyter lab --config=/home/<USER>/.jupyter/jupyter_lab_config.py
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable jupyter-lab
sudo systemctl start jupyter-lab
sudo systemctl status jupyter-lab
```

## Security Best Practices

### 1. Use Tailscale ACLs
Add to your Tailscale ACL policy:

```json
{
  "acls": [
    {
      "action": "accept",
      "src": ["group:lab-users"],
      "dst": ["your-machine:8888"]
    }
  ],
  "groups": {
    "group:lab-users": [
      "<EMAIL>",
      "<EMAIL>"
    ]
  }
}
```

### 2. Enable SSL/TLS
Generate self-signed certificate:

```bash
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout jupyter.key -out jupyter.crt
```

### 3. Use Strong Authentication
- Always use password or token authentication
- Consider using JupyterHub for multi-user setups
- Enable 2FA if possible

### 4. Monitor Access
Check Jupyter logs:
```bash
journalctl -u jupyter-lab -f  # If using systemd
# Or
tail -f ~/.local/share/jupyter/runtime/jpserver-*.log
```

## Quick Start for PPDesign

For the PPDesign project specifically:

```bash
#!/bin/bash
# start_ppdesign_jupyter.sh

# Activate pixi environment
cd /home/<USER>/dev/ppdesign

# Get Tailscale IP
TAILSCALE_IP=$(tailscale ip -4)
HOSTNAME=$(hostname)

echo "=================================================="
echo "Starting PPDesign Jupyter Lab"
echo "=================================================="
echo "Tailscale IP: $TAILSCALE_IP"
echo "Hostname: $HOSTNAME"
echo ""
echo "Access from Tailscale network:"
echo "  http://$TAILSCALE_IP:8888"
echo "  http://$HOSTNAME:8888"
echo "=================================================="

# Start Jupyter with PPDesign environment
pixi run jupyter lab \
    --ip=$TAILSCALE_IP \
    --port=8888 \
    --no-browser \
    --NotebookApp.password='argon2:$argon2id$v=19$m=10240,t=10,p=8$...' \
    --NotebookApp.allow_origin='*' \
    --NotebookApp.allow_remote_access=True \
    --notebook-dir=.
```

## Accessing from Team Members

Team members on your Tailscale network can access via:

1. **Machine name**: `http://your-machine-name:8888`
2. **Tailscale IP**: `http://100.x.x.x:8888`
3. **MagicDNS name**: `http://your-machine.tail-scale.ts.net:8888`

## Troubleshooting

### Port Already in Use
```bash
# Find process using port 8888
lsof -i :8888
# Kill it if needed
kill -9 <PID>
```

### Can't Connect from Tailscale
```bash
# Check if Jupyter is listening on correct interface
netstat -tlnp | grep 8888

# Check Tailscale status
tailscale status

# Test connectivity from another Tailscale machine
curl http://your-tailscale-ip:8888
```

### Firewall Issues
```bash
# Ubuntu/Debian
sudo ufw allow from **********/10 to any port 8888

# Or allow specific Tailscale subnet
sudo ufw allow from 100.0.0.0/8 to any port 8888
```

## Alternative: Code Server (VS Code in Browser)

For a full IDE experience, consider code-server:

```bash
# Install code-server
curl -fsSL https://code-server.dev/install.sh | sh

# Run with Tailscale IP
code-server --bind-addr $(tailscale ip -4):8080 \
    --auth password \
    --cert false
```

This gives you VS Code in the browser with full access to the PPDesign project!