version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/bioconda/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-25.1.0-py312h4c3975b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/biopython-1.85-py312h66e93f0_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/blast-2.16.0-h66d330f_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/cd-hit-4.8.1-h5ca1c30_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py312h68727a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.28-hd9c7081_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.16-py312h8285ef7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.12-h13889ed_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/entrez-direct-22.4-he881be0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.59.0-py312h8a5da7c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.3.2-hbb57e21_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh82676e8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py312h7900ff3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/k8-1.2-he8db53b_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.8-py312h68727a3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.25.1-h3f43e3d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.8-default_hddf928d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-20.1.8-default_ha444ac7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-hb8b1518_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.25.1-h3f43e3d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libidn2-2.3.8-ha4ef2c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapacke-3.9.0-32_he2f377e_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.8-hecd9e04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.5-h27ae623_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.3-hee844dc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunistring-0.9.10-h7f98852_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.43-h7a3aeb2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mafft-7.526-h4bc722e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py312h178313f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.3-py312h7900ff3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py312hd3ec401_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/minimap2-2.30-h577a1d6_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/muscle-5.3-h9948957_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/ncbi-vdb-3.2.1-h9948957_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.1-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.1-py312hf79963d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/parasail-python-1.3.4-py312hdcc493e_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-bzip2-2.201-pl5321hbf60520_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-zlib-2.202-pl5321hadc24fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-encode-3.21-pl5321hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-io-compress-2.201-pl5321h503566f_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-json-xs-4.03-pl5321h9948957_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-list-moreutils-xs-0.430-pl5321h7b50bb2_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-pathtools-3.75-pl5321hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-scalar-list-utils-1.69-pl5321hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-storable-3.15-pl5321hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py312h80c1187_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.4-h537e5f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/polars-1.31.0-default_h70f2ef1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/polars-default-1.31.0-py39hf521cc8_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/prodigal-gv-2.11.0-h577a1d6_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/proteinortho-6.3.6-h2b77389_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.9.1-py312hdb827e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.11-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-duckdb-1.3.2-py312h1289d80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py312h178313f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.1-py312h6748674_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.9.1-h6ac528c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.27.0-py312h868fb18_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/rpsbproc-0.5.0-hd6d6fdc_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.7.1-py312h4f0b9e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.0-py312hf734454_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/statsmodels-0.14.5-py312h8b63200_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.24.0-h3e06ad9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wget-1.21.4-hda4d442_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      osx-64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/argon2-cffi-bindings-25.1.0-py312h2f459f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/aria2-1.37.0-h21e4757_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/biopython-1.85-py312h01d7ebd_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/blast-2.16.0-h53185c9_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-python-1.1.0-py312haafddd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/c-ares-1.34.5-hf13058a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/cd-hit-4.8.1-h24b48ac_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/cffi-1.17.1-py312hf857d28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.2-py312hc47a885_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/curl-8.14.1-h5dec5d8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.16-py312h2ac44ba_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/diamond-2.1.12-h9bd2776_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/entrez-direct-22.4-h193322a_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.59.0-py312h3d55d04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/freetype-2.13.3-h694c41f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/gawk-5.3.1-h0631170_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/gmp-6.3.0-hf036a51_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/jsonpointer-3.0.0-py312hb401068_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/k8-1.2-h2ec61ea_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/kiwisolver-1.4.8-py312hc47a885_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/krb5-1.21.3-h37d8d59_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lcms2-2.17-h72f5680_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lerc-4.0.0-hcca01a6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libasprintf-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-32_h7f60823_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlicommon-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlidec-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlienc-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-32_hff6cab4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcurl-8.14.1-h5dec5d8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-20.1.8-h3d58e20_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libdeflate-1.24-hcc1b750_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libev-4.33-h10d778d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.13.3-h694c41f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.13.3-h40dfd5c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgettextpo-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-5.0.0-14_2_0_h51e75f0_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-14.2.0-h51e75f0_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libiconv-1.18-h4b5e92a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libidn2-2.3.8-he8ff88c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libintl-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-32_h236ab99_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapacke-3.9.0-32_h85686d2_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libnghttp2-1.64.0-hc7306c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_hbf64a52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libpng-1.6.50-h3c4a55f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsodium-1.0.20-hfdf4475_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.3-h875aaf5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libssh2-1.11.1-hed3591d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libtiff-4.7.0-h1167cee_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libunistring-0.9.10-h0d85af4_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libwebp-base-1.6.0-hb807250_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libxcb-1.17.0-hf1f96e2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libxml2-2.13.8-h93c44a6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-20.1.8-hf4e0ed4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/mafft-7.526-hfdf4475_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/markupsafe-3.0.2-py312h3520af0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-3.10.3-py312hb401068_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-base-3.10.3-py312h535dea3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/minimap2-2.30-h7f84b70_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/mmseqs2-17.b804f-h8b377d6_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/mpfr-4.2.1-haed47dc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/muscle-5.3-h8e8ab34_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/ncbi-vdb-3.2.1-h5fa12a8_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py312hda18a35_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openjpeg-2.5.3-h7fd6d84_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.1-hc426f3f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pandas-2.3.1-py312hbf2c5ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/parasail-python-1.3.4-py312h13dbd8f_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-5.32.1-7_h10d778d_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-bzip2-2.201-pl5321haeee4d3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-zlib-2.202-pl5321h0aa47d9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-encode-3.21-pl5321h6e16a3a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-io-compress-2.201-pl5321h5eaf441_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-json-xs-4.03-pl5321h5fa12a8_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-list-moreutils-xs-0.430-pl5321h18d8cf3_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-pathtools-3.75-pl5321h6e16a3a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-scalar-list-utils-1.69-pl5321h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-storable-3.15-pl5321h6e16a3a_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pillow-11.3.0-py312hd9f36e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/polars-1.31.0-default_h1ec6524_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/polars-default-1.31.0-py39hbd2d40b_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/prodigal-gv-2.11.0-h7f84b70_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/proteinortho-6.3.6-hdcb8ee1_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/psutil-7.0.0-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pthread-stubs-0.4-h00291cd_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-core-11.1-py312h3f2cce9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-framework-cocoa-11.1-py312h2365019_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.12.11-h9ccd52b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-duckdb-1.3.2-py312h462f358_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyyaml-6.0.2-py312h3520af0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyzmq-27.0.1-py312hbb7883b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/qhull-2020.2-h3c5361c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/rpds-py-0.27.0-py312h00ff6fd_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/rpsbproc-0.5.0-heca6186_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/scikit-learn-1.7.1-py312hf34d0c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/scipy-1.16.0-py312hd0c0319_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/statsmodels-0.14.5-py312h34a05c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.1-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/wget-1.21.4-hca547e6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxau-1.0.12-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxdmcp-1.1.5-h00291cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/yaml-0.2.5-h0d85af4_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zeromq-4.3.5-h7130eaa_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.23.0-py312h01d7ebd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstd-1.5.7-h8210216_2.conda
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/argon2-cffi-bindings-25.1.0-py312h163523d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/aria2-1.37.0-hfa5e12f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/biopython-1.85-py312hea69d52_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/blast-2.16.0-hb260f6e_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-bin-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-python-1.1.0-py312hd8f9ff3_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/c-ares-1.34.5-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/cd-hit-4.8.1-haf7d672_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/cffi-1.17.1-py312h0fad829_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/contourpy-1.3.2-py312hb23fbb9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/curl-8.14.1-h73640d1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/debugpy-1.8.16-py312he360a15_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/diamond-2.1.12-ha27f01c_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/entrez-direct-22.4-hd5f1084_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/fonttools-4.59.0-py312h6daa0e5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gawk-5.3.1-h8a92848_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gmp-6.3.0-h7bae524_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/jsonpointer-3.0.0-py312h81bd7bf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/k8-1.2-hda5e58c_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/kiwisolver-1.4.8-py312hb23fbb9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lcms2-2.17-h7eeda09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlicommon-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlidec-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlienc-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcurl-8.14.1-h73640d1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.8-hf598326_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libev-4.33-h93a5062_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.13.3-h1d14073_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libiconv-1.18-hfe07756_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libidn2-2.3.8-h38aa460_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapacke-3.9.0-32_hbb7bcf8_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libnghttp2-1.64.0-h6d7220d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.50-h3783ad8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.3-h4237e3c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libssh2-1.11.1-h1590b86_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h2f21f7c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libunistring-0.9.10-h3422bc3_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.6.0-h07db88b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxcb-1.17.0-hdb1d25a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxml2-2.13.8-h52572c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.8-hbb9b287_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mafft-7.526-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/markupsafe-3.0.2-py312h998013c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-3.10.3-py312h1f38498_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-base-3.10.3-py312hdbc7e53_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/minimap2-2.30-hba9b596_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/mmseqs2-17.b804f-h44b2af9_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mpfr-4.2.1-hb693164_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/muscle-5.3-h28ef24b_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/ncbi-vdb-3.2.1-h4675bf2_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py312h2f38b44_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openjpeg-2.5.3-h8a3d83b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.1-h81ee809_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pandas-2.3.1-py312h98f7732_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/parasail-python-1.3.4-py312hd60a339_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-5.32.1-7_h4614cfb_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-bzip2-2.201-pl5321h9337747_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-zlib-2.202-pl5321hb76e6fb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-encode-3.21-pl5321h5505292_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-io-compress-2.201-pl5321haef7865_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-json-xs-4.03-pl5321h4675bf2_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-list-moreutils-xs-0.430-pl5321hbdacb55_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-pathtools-3.75-pl5321hc71e825_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-scalar-list-utils-1.69-pl5321hc71e825_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-storable-3.15-pl5321hc71e825_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pillow-11.3.0-py312h50aef2c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-1.31.0-default_h13af070_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-default-1.31.0-py39h31c57e4_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/prodigal-gv-2.11.0-hba9b596_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/proteinortho-6.3.6-hd1e0bca_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/psutil-7.0.0-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pthread-stubs-0.4-hd74edd7_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-core-11.1-py312h4c66426_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-framework-cocoa-11.1-py312hb9d441b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.12.11-hc22306f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-duckdb-1.3.2-py312h6b01ec3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyyaml-6.0.2-py312h998013c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.1-py312h211b278_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/qhull-2020.2-h420ef59_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/rpds-py-0.27.0-py312h6f58b40_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/rpsbproc-0.5.0-hf8bb5b5_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/scikit-learn-1.7.1-py312h54d6233_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/scipy-1.16.0-py312hcedbd36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/statsmodels-0.14.5-py312hcde60ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/unicodedata2-16.0.0-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/wget-1.21.4-he2df1f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxau-1.0.12-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxdmcp-1.1.5-hd74edd7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/yaml-0.2.5-h3422bc3_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstandard-0.23.0-py312hea69d52_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
  dev:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/bioconda/
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-25.1.0-py312h4c3975b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backrefs-5.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/biopython-1.85-py312h66e93f0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/black-25.1.0-pyh866005b_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/blast-2.16.0-h66d330f_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/cd-hit-4.8.1-h5ca1c30_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py312h68727a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/coverage-7.10.0-py312h8a5da7c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.28-hd9c7081_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.16-py312h8285ef7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.12-h13889ed_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/entrez-direct-22.4-he881be0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.59.0-py312h8a5da7c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ghp-import-2.1.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/griffe-1.8.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.3.2-hbb57e21_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.6.12-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh82676e8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py312h7900ff3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/k8-1.2-he8db53b_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.8-py312h68727a3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.25.1-h3f43e3d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.8-default_hddf928d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-20.1.8-default_ha444ac7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-hb8b1518_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.25.1-h3f43e3d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libidn2-2.3.8-ha4ef2c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapacke-3.9.0-32_he2f377e_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.8-hecd9e04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.5-h27ae623_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.3-hee844dc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunistring-0.9.10-h7f98852_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.43-h7a3aeb2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mafft-7.526-h4bc722e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-3.8.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py312h178313f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.3-py312h7900ff3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py312hd3ec401_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mergedeep-1.3.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/minimap2-2.30-h577a1d6_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-1.6.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-autorefs-1.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-get-deps-0.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-9.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-extensions-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-0.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-python-1.16.12-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/muscle-5.3-h9948957_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mypy-1.17.0-py312h4c3975b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/ncbi-vdb-3.2.1-h9948957_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.9.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.1-h7b32b05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/paginate-0.5.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.1-py312hf79963d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/parasail-python-1.3.4-py312hdcc493e_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-bzip2-2.201-pl5321hbf60520_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-zlib-2.202-pl5321hadc24fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-encode-3.21-pl5321hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-io-compress-2.201-pl5321h503566f_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-json-xs-4.03-pl5321h9948957_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/linux-64/perl-list-moreutils-xs-0.430-pl5321h7b50bb2_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-pathtools-3.75-pl5321hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-scalar-list-utils-1.69-pl5321hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/perl-storable-3.15-pl5321hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py312h80c1187_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.4-h537e5f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/polars-1.31.0-default_h70f2ef1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/polars-default-1.31.0-py39hf521cc8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-4.2.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/prodigal-gv-2.11.0-h577a1d6_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/proteinortho-6.3.6-h2b77389_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pymdown-extensions-10.16-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.9.1-py312hdb827e4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-cov-6.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.11-h9e4cc4f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-duckdb-1.3.2-py312h1289d80_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py312h178313f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyyaml-env-tag-1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.1-py312h6748674_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.9.1-h6ac528c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.27.0-py312h868fb18_0.conda
      - conda: https://conda.anaconda.org/bioconda/linux-64/rpsbproc-0.5.0-hd6d6fdc_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ruff-0.12.5-hf9daec2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.7.1-py312h4f0b9e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.0-py312hf734454_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/statsmodels-0.14.5-py312h8b63200_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/toml-0.10.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ukkonen-1.0.1-py312h68727a3_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py312h66e93f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.32.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/watchdog-6.0.0-py312h7900ff3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.24.0-h3e06ad9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wget-1.21.4-hda4d442_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      osx-64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/argon2-cffi-bindings-25.1.0-py312h2f459f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/aria2-1.37.0-h21e4757_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backrefs-5.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/biopython-1.85-py312h01d7ebd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/black-25.1.0-pyh866005b_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/blast-2.16.0-h53185c9_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-python-1.1.0-py312haafddd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/c-ares-1.34.5-hf13058a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/cd-hit-4.8.1-h24b48ac_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/cffi-1.17.1-py312hf857d28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.2-py312hc47a885_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/coverage-7.10.0-py312h3d55d04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/curl-8.14.1-h5dec5d8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.16-py312h2ac44ba_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/diamond-2.1.12-h9bd2776_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/entrez-direct-22.4-h193322a_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.59.0-py312h3d55d04_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/freetype-2.13.3-h694c41f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/gawk-5.3.1-h0631170_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ghp-import-2.1.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/gmp-6.3.0-hf036a51_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/griffe-1.8.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.6.12-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/jsonpointer-3.0.0-py312hb401068_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/k8-1.2-h2ec61ea_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/kiwisolver-1.4.8-py312hc47a885_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/krb5-1.21.3-h37d8d59_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lcms2-2.17-h72f5680_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/lerc-4.0.0-hcca01a6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libasprintf-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-32_h7f60823_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlicommon-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlidec-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlienc-1.1.0-h6e16a3a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-32_hff6cab4_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcurl-8.14.1-h5dec5d8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-20.1.8-h3d58e20_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libdeflate-1.24-hcc1b750_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libev-4.33-h10d778d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.13.3-h694c41f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.13.3-h40dfd5c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgettextpo-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-5.0.0-14_2_0_h51e75f0_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-14.2.0-h51e75f0_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libiconv-1.18-h4b5e92a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libidn2-2.3.8-he8ff88c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libintl-0.25.1-h3184127_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-32_h236ab99_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblapacke-3.9.0-32_h85686d2_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libnghttp2-1.64.0-hc7306c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_hbf64a52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libpng-1.6.50-h3c4a55f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsodium-1.0.20-hfdf4475_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.3-h875aaf5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libssh2-1.11.1-hed3591d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libtiff-4.7.0-h1167cee_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libunistring-0.9.10-h0d85af4_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libwebp-base-1.6.0-hb807250_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libxcb-1.17.0-hf1f96e2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libxml2-2.13.8-h93c44a6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-20.1.8-hf4e0ed4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/mafft-7.526-hfdf4475_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-3.8.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/markupsafe-3.0.2-py312h3520af0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-3.10.3-py312hb401068_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-base-3.10.3-py312h535dea3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mergedeep-1.3.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/minimap2-2.30-h7f84b70_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-1.6.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-autorefs-1.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-get-deps-0.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-9.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-extensions-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-0.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-python-1.16.12-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/mmseqs2-17.b804f-h8b377d6_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/mpfr-4.2.1-haed47dc_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/muscle-5.3-h8e8ab34_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/mypy-1.17.0-py312h2f459f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/ncbi-vdb-3.2.1-h5fa12a8_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.9.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py312hda18a35_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openjpeg-2.5.3-h7fd6d84_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.1-hc426f3f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/paginate-0.5.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pandas-2.3.1-py312hbf2c5ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/parasail-python-1.3.4-py312h13dbd8f_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-5.32.1-7_h10d778d_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-bzip2-2.201-pl5321haeee4d3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-zlib-2.202-pl5321h0aa47d9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-encode-3.21-pl5321h6e16a3a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-io-compress-2.201-pl5321h5eaf441_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-json-xs-4.03-pl5321h5fa12a8_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-64/perl-list-moreutils-xs-0.430-pl5321h18d8cf3_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-pathtools-3.75-pl5321h6e16a3a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-scalar-list-utils-1.69-pl5321h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/perl-storable-3.15-pl5321h6e16a3a_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pillow-11.3.0-py312hd9f36e3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/polars-1.31.0-default_h1ec6524_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/polars-default-1.31.0-py39hbd2d40b_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-4.2.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/prodigal-gv-2.11.0-h7f84b70_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/proteinortho-6.3.6-hdcb8ee1_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/psutil-7.0.0-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pthread-stubs-0.4-h00291cd_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pymdown-extensions-10.16-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-core-11.1-py312h3f2cce9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-framework-cocoa-11.1-py312h2365019_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-cov-6.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.12.11-h9ccd52b_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/python-duckdb-1.3.2-py312h462f358_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyyaml-6.0.2-py312h3520af0_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyyaml-env-tag-1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/pyzmq-27.0.1-py312hbb7883b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/qhull-2020.2-h3c5361c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/rpds-py-0.27.0-py312h00ff6fd_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-64/rpsbproc-0.5.0-heca6186_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ruff-0.12.5-h6cc4cfe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/scikit-learn-1.7.1-py312hf34d0c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/scipy-1.16.0-py312hd0c0319_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/statsmodels-0.14.5-py312h34a05c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/toml-0.10.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.1-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/ukkonen-1.0.1-py312hc5c4d5f_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.32.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/watchdog-6.0.0-py312h01d7ebd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/wget-1.21.4-hca547e6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxau-1.0.12-h6e16a3a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxdmcp-1.1.5-h00291cd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/yaml-0.2.5-h0d85af4_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zeromq-4.3.5-h7130eaa_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zlib-1.3.1-hd23fc13_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.23.0-py312h01d7ebd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-64/zstd-1.5.7-h8210216_2.conda
      osx-arm64:
      - conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/argon2-cffi-bindings-25.1.0-py312h163523d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/aria2-1.37.0-hfa5e12f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/backrefs-5.8-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/biopython-1.85-py312hea69d52_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/black-25.1.0-pyh866005b_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/blast-2.16.0-hb260f6e_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-bin-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-python-1.1.0-py312hd8f9ff3_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/c-ares-1.34.5-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/cd-hit-4.8.1-haf7d672_13.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/cffi-1.17.1-py312h0fad829_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/contourpy-1.3.2-py312hb23fbb9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/coverage-7.10.0-py312h6daa0e5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/curl-8.14.1-h73640d1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/debugpy-1.8.16-py312he360a15_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/diamond-2.1.12-ha27f01c_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/entrez-direct-22.4-hd5f1084_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/fonttools-4.59.0-py312h6daa0e5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gawk-5.3.1-h8a92848_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ghp-import-2.1.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/gmp-6.3.0-h7bae524_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/griffe-1.8.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.6.12-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/jsonpointer-3.0.0-py312h81bd7bf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/k8-1.2-hda5e58c_6.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/kiwisolver-1.4.8-py312hb23fbb9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lcms2-2.17-h7eeda09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlicommon-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlidec-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlienc-1.1.0-h5505292_3.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcurl-8.14.1-h73640d1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.8-hf598326_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libev-4.33-h93a5062_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.13.3-hce30654_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.13.3-h1d14073_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libiconv-1.18-hfe07756_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libidn2-2.3.8-h38aa460_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-0.25.1-h493aca8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapacke-3.9.0-32_hbb7bcf8_openblas.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libnghttp2-1.64.0-h6d7220d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.50-h3783ad8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.3-h4237e3c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libssh2-1.11.1-h1590b86_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h2f21f7c_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libunistring-0.9.10-h3422bc3_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.6.0-h07db88b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxcb-1.17.0-hdb1d25a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxml2-2.13.8-h52572c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.8-hbb9b287_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mafft-7.526-h99b78c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-3.8.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/markupsafe-3.0.2-py312h998013c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-3.10.3-py312h1f38498_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-base-3.10.3-py312hdbc7e53_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mergedeep-1.3.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/minimap2-2.30-hba9b596_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-1.6.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-autorefs-1.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-get-deps-0.2.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-9.6.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-extensions-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-0.30.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-python-1.16.12-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/mmseqs2-17.b804f-h44b2af9_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mpfr-4.2.1-hb693164_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/muscle-5.3-h28ef24b_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/mypy-1.17.0-py312h163523d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/ncbi-vdb-3.2.1-h4675bf2_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.9.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py312h2f38b44_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openjpeg-2.5.3-h8a3d83b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.1-h81ee809_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/paginate-0.5.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pandas-2.3.1-py312h98f7732_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/parasail-python-1.3.4-py312hd60a339_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-5.32.1-7_h4614cfb_perl5.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-bzip2-2.201-pl5321h9337747_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-zlib-2.202-pl5321hb76e6fb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-encode-3.21-pl5321h5505292_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-io-compress-2.201-pl5321haef7865_5.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-json-xs-4.03-pl5321h4675bf2_4.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-list-moreutils-xs-0.430-pl5321hbdacb55_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-pathtools-3.75-pl5321hc71e825_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-scalar-list-utils-1.69-pl5321hc71e825_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-storable-3.15-pl5321hc71e825_2.conda
      - conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pillow-11.3.0-py312h50aef2c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-1.31.0-default_h13af070_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-default-1.31.0-py39h31c57e4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-4.2.0-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/prodigal-gv-2.11.0-hba9b596_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/proteinortho-6.3.6-hd1e0bca_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/psutil-7.0.0-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pthread-stubs-0.4-hd74edd7_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pymdown-extensions-10.16-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-core-11.1-py312h4c66426_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-framework-cocoa-11.1-py312hb9d441b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-cov-6.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.12.11-hc22306f_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-duckdb-1.3.2-py312h6b01ec3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyyaml-6.0.2-py312h998013c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyyaml-env-tag-1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.1-py312h211b278_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/qhull-2020.2-h420ef59_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/rpds-py-0.27.0-py312h6f58b40_0.conda
      - conda: https://conda.anaconda.org/bioconda/osx-arm64/rpsbproc-0.5.0-hf8bb5b5_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ruff-0.12.5-h575f11b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/scikit-learn-1.7.1-py312h54d6233_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/scipy-1.16.0-py312hcedbd36_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/statsmodels-0.14.5-py312hcde60ef_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/toml-0.10.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/ukkonen-1.0.1-py312h6142ec9_5.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/unicodedata2-16.0.0-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.32.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/watchdog-6.0.0-py312hea69d52_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/wget-1.21.4-he2df1f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxau-1.0.12-h5505292_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxdmcp-1.1.5-hd74edd7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/yaml-0.2.5-h3422bc3_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zlib-1.3.1-h8359307_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstandard-0.23.0-py312hea69d52_2.conda
      - conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/noarch/_python_abi3_support-1.0-hd8ed1ab_2.conda
  sha256: a3967b937b9abf0f2a99f3173fa4630293979bd1644709d89580e7c62a544661
  md5: aaa2a381ccc56eac91d63b6c1240312f
  depends:
  - cpython
  - python-gil
  license: MIT
  license_family: MIT
  size: 8191
  timestamp: 1744137672556
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.10.0-pyhe01879c_0.conda
  sha256: d1b50686672ebe7041e44811eda563e45b94a8354db67eca659040392ac74d63
  md5: cc2613bfa71dec0eb2113ee21ac9ccbf
  depends:
  - exceptiongroup >=1.0.2
  - idna >=2.8
  - python >=3.9
  - sniffio >=1.1
  - typing_extensions >=4.5
  - python
  constrains:
  - trio >=0.26.1
  - uvloop >=0.21
  license: MIT
  license_family: MIT
  size: 134857
  timestamp: 1754315087747
- conda: https://conda.anaconda.org/conda-forge/noarch/appnope-0.1.4-pyhd8ed1ab_1.conda
  sha256: 8f032b140ea4159806e4969a68b4a3c0a7cab1ad936eb958a2b5ffe5335e19bf
  md5: 54898d0f524c9dee622d44bbb081a8ab
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 10076
  timestamp: 1733332433806
- conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-25.1.0-pyhd8ed1ab_0.conda
  sha256: bea62005badcb98b1ae1796ec5d70ea0fc9539e7d59708ac4e7d41e2f4bb0bad
  md5: 8ac12aff0860280ee0cff7fa2cf63f3b
  depends:
  - argon2-cffi-bindings
  - python >=3.9
  - typing-extensions
  constrains:
  - argon2_cffi ==999
  license: MIT
  license_family: MIT
  size: 18715
  timestamp: 1749017288144
- conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-25.1.0-py312h4c3975b_0.conda
  sha256: d072b579af12d86e239487cea16ec860e2bc2f26edca9f9697a5b3a031735228
  md5: fdcda5c2e5c6970e9f629c37ec321037
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.0.1
  - libgcc >=14
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 35575
  timestamp: 1753994865409
- conda: https://conda.anaconda.org/conda-forge/osx-64/argon2-cffi-bindings-25.1.0-py312h2f459f6_0.conda
  sha256: 7295349162f33f59cc2240abf0cb5e25317d8ab7989fc1ec224a8ce3963c69bf
  md5: 4cc34c91c812d0bf641d8b0a9c221ffd
  depends:
  - __osx >=10.13
  - cffi >=1.0.1
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 33388
  timestamp: 1753994995015
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/argon2-cffi-bindings-25.1.0-py312h163523d_0.conda
  sha256: 60a08028fdaf9c00477e1c3372d0c6e66680581e6f85bca907c6add7d6868258
  md5: 1859c76d7f1e215924d544d7a0e9697d
  depends:
  - __osx >=11.0
  - cffi >=1.0.1
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 34110
  timestamp: 1753994992104
- conda: https://conda.anaconda.org/conda-forge/linux-64/aria2-1.37.0-hbc8128a_2.conda
  sha256: 06ac389ee45049af40aeb9940eacef92f04d6b5741fc1154be282f420479a49f
  md5: 03b8874fa70df577f3eee53085d025cf
  depends:
  - c-ares >=1.28.1,<2.0a0
  - libgcc-ng >=12
  - libsqlite >=3.46.0,<4.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libstdcxx-ng >=12
  - libxml2 >=2.12.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.1,<4.0a0
  license: GPL-2.0-only
  license_family: GPL
  size: 1638055
  timestamp: 1718840932941
- conda: https://conda.anaconda.org/conda-forge/osx-64/aria2-1.37.0-h21e4757_2.conda
  sha256: 29f825a69375ab4d7a520e0ee9a2f8d9ee43d837a07d7924a345206525faf12d
  md5: 97c11126cf0a5a10402e8999ec4bec13
  depends:
  - __osx >=10.13
  - c-ares >=1.28.1,<2.0a0
  - gmp >=6.3.0,<7.0a0
  - libcxx >=16
  - libsqlite >=3.46.0,<4.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libxml2 >=2.12.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-only
  license_family: GPL
  size: 1143840
  timestamp: 1718841890905
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/aria2-1.37.0-hfa5e12f_2.conda
  sha256: 2d8fe16a5daae83ce7058df7b4de3b1f0364b202ffce12a375a187921826d8ba
  md5: 640384ccd1a42f8330897bf2adca78cf
  depends:
  - __osx >=11.0
  - c-ares >=1.28.1,<2.0a0
  - gmp >=6.3.0,<7.0a0
  - libcxx >=16
  - libsqlite >=3.46.0,<4.0a0
  - libssh2 >=1.11.0,<2.0a0
  - libxml2 >=2.12.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-only
  license_family: GPL
  size: 1067782
  timestamp: 1718841368507
- conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
  sha256: c4b0bdb3d5dee50b60db92f99da3e4c524d5240aafc0a5fcc15e45ae2d1a3cd1
  md5: 46b53236fdd990271b03c3978d4218a9
  depends:
  - python >=3.9
  - python-dateutil >=2.7.0
  - types-python-dateutil >=2.8.10
  license: Apache-2.0
  license_family: Apache
  size: 99951
  timestamp: 1733584345583
- conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
  sha256: 93b14414b3b3ed91e286e1cbe4e7a60c4e1b1c730b0814d1e452a8ac4b9af593
  md5: 8f587de4bcf981e26228f268df374a9b
  depends:
  - python >=3.9
  constrains:
  - astroid >=2,<4
  license: Apache-2.0
  license_family: Apache
  size: 28206
  timestamp: 1733250564754
- conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
  sha256: 3b7233041e462d9eeb93ea1dfe7b18aca9c358832517072054bb8761df0c324b
  md5: d9d0f99095a9bb7e3641bca8c6ad2ac7
  depends:
  - python >=3.9
  - typing_extensions >=4.0.0
  - python
  license: MIT
  license_family: MIT
  size: 17335
  timestamp: 1742153708859
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
  sha256: 1c656a35800b7f57f7371605bc6507c8d3ad60fbaaec65876fce7f73df1fc8ac
  md5: 0a01c169f0ab0f91b26e77a3301fbfe4
  depends:
  - python >=3.9
  - pytz >=2015.7
  license: BSD-3-Clause
  license_family: BSD
  size: 6938256
  timestamp: 1738490268466
- conda: https://conda.anaconda.org/conda-forge/noarch/backrefs-5.8-pyhd8ed1ab_0.conda
  sha256: 3a0af23d357a07154645c41d035a4efbd15b7a642db397fa9ea0193fd58ae282
  md5: b16e2595d3a9042aa9d570375978835f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 143810
  timestamp: 1740887689966
- conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
  sha256: ddb0df12fd30b2d36272f5daf6b6251c7625d6a99414d7ea930005bbaecad06d
  md5: 9f07c4fc992adb2d6c30da7fab3959a7
  depends:
  - python >=3.9
  - soupsieve >=1.2
  - typing-extensions
  license: MIT
  license_family: MIT
  size: 146613
  timestamp: 1744783307123
- conda: https://conda.anaconda.org/conda-forge/linux-64/biopython-1.85-py312h66e93f0_1.conda
  sha256: 811aadba96f8f1cd2c57eb31bf58919d544ceb81e55126ac15b657fa2cd23ed0
  md5: 1d1f8838e26ff73784990e7ca8e4b9a5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - numpy
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: LicenseRef-Biopython
  size: 3476893
  timestamp: 1737241855271
- conda: https://conda.anaconda.org/conda-forge/osx-64/biopython-1.85-py312h01d7ebd_1.conda
  sha256: 7e9cc9e7ae68a7a1d4e89901c454b8f9152fee02263e4f46a0e351840a268834
  md5: aa9e5684dcb5fec23ce67b45f9a3730e
  depends:
  - __osx >=10.13
  - numpy
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: LicenseRef-Biopython
  size: 3466765
  timestamp: 1737241911497
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/biopython-1.85-py312hea69d52_1.conda
  sha256: e11cbcddcaa0595c10d922690aadd438c19a061a03037ca3233a357b9c9e49c3
  md5: 46d31ef33d4f025a2f61432f4cedefb5
  depends:
  - __osx >=11.0
  - numpy
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: LicenseRef-Biopython
  size: 3476810
  timestamp: 1737242019234
- conda: https://conda.anaconda.org/conda-forge/noarch/black-25.1.0-pyh866005b_0.conda
  sha256: c68f110cd491dc839a69e340930862e54c00fb02cede5f1831fcf8a253bd68d2
  md5: b9b0c42e7316aa6043bdfd49883955b8
  depends:
  - click >=8.0.0
  - mypy_extensions >=0.4.3
  - packaging >=22.0
  - pathspec >=0.9
  - platformdirs >=2
  - python >=3.11
  license: MIT
  license_family: MIT
  size: 172678
  timestamp: 1742502887437
- conda: https://conda.anaconda.org/bioconda/linux-64/blast-2.16.0-h66d330f_5.tar.bz2
  sha256: 03c8c42f9caca6233ba21e83c955b99f910564ff2572f2c4739e1af2b77f7931
  md5: 566efaf2b6144713ef7baac917956566
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - curl
  - entrez-direct >=22.4,<23.0a0
  - libgcc >=13
  - libsqlite >=3.49.1,<4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - ncbi-vdb >=3.2.1,<4.0a0
  - perl
  - perl-archive-tar
  - perl-json
  - perl-list-moreutils
  - rpsbproc
  - zlib
  license: NCBI-PD
  size: 148270198
  timestamp: 1743181320604
- conda: https://conda.anaconda.org/bioconda/osx-64/blast-2.16.0-h53185c9_5.tar.bz2
  sha256: 2ef6e312ecbf718d93c6811fd18b4d553ddcaa0421eb396f4ad010baae2e54de
  md5: f7f716d19363669c6fef607d9f782553
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - curl
  - entrez-direct >=22.4,<23.0a0
  - libcxx >=18
  - libsqlite >=3.49.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncbi-vdb >=3.2.1,<4.0a0
  - perl
  - perl-archive-tar
  - perl-json
  - perl-list-moreutils
  - rpsbproc
  - zlib
  license: NCBI-PD
  size: 168734931
  timestamp: 1743189752491
- conda: https://conda.anaconda.org/bioconda/osx-arm64/blast-2.16.0-hb260f6e_5.tar.bz2
  sha256: 0ca5da8fa1aee16a96bfc00284f09a92bd85aa6f57ded3a9ab6a21d573bec52f
  md5: 112829a9e327f78c8f26699c19d65bfb
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - curl
  - entrez-direct >=22.4,<23.0a0
  - libcxx >=18
  - libsqlite >=3.49.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncbi-vdb >=3.2.1,<4.0a0
  - perl
  - perl-archive-tar
  - perl-json
  - perl-list-moreutils
  - rpsbproc
  - zlib
  license: NCBI-PD
  size: 157885023
  timestamp: 1743182400505
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
  sha256: a05971bb80cca50ce9977aad3f7fc053e54ea7d5321523efc7b9a6e12901d3cd
  md5: f0b4c8e370446ef89797608d60a564b3
  depends:
  - python >=3.9
  - webencodings
  - python
  constrains:
  - tinycss >=1.1.0,<1.5
  license: Apache-2.0 AND MIT
  size: 141405
  timestamp: 1737382993425
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
  sha256: 0aba699344275b3972bd751f9403316edea2ceb942db12f9f493b63c74774a46
  md5: a30e9406c873940383555af4c873220d
  depends:
  - bleach ==6.2.0 pyh29332c3_4
  - tinycss2
  license: Apache-2.0 AND MIT
  size: 4213
  timestamp: 1737382993425
- conda: https://conda.anaconda.org/conda-forge/noarch/bokeh-3.7.3-pyhd8ed1ab_0.conda
  sha256: dd116a77a5aca118cfdfcc97553642295a3fb176a4e741fd3d1363ee81cebdfd
  md5: 708d2f99b8a2c833ff164a225a265e76
  depends:
  - contourpy >=1.2
  - jinja2 >=2.9
  - narwhals >=1.13
  - numpy >=1.16
  - packaging >=16.8
  - pandas >=1.2
  - pillow >=7.1.0
  - python >=3.10
  - pyyaml >=3.10
  - tornado >=6.2
  - xyzservices >=2021.09.1
  license: BSD-3-Clause
  license_family: BSD
  size: 4934851
  timestamp: 1747091638593
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_3.conda
  sha256: c969baaa5d7a21afb5ed4b8dd830f82b78e425caaa13d717766ed07a61630bec
  md5: 5d08a0ac29e6a5a984817584775d4131
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli-bin 1.1.0 hb9d3cd8_3
  - libbrotlidec 1.1.0 hb9d3cd8_3
  - libbrotlienc 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19810
  timestamp: 1749230148642
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-1.1.0-h6e16a3a_3.conda
  sha256: cd44fe22eeb1dec1ec52402f149faebb5f304f39bf59d97eb56f4c0f41e051d8
  md5: 44903b29bc866576c42d5c0a25e76569
  depends:
  - __osx >=10.13
  - brotli-bin 1.1.0 h6e16a3a_3
  - libbrotlidec 1.1.0 h6e16a3a_3
  - libbrotlienc 1.1.0 h6e16a3a_3
  license: MIT
  license_family: MIT
  size: 19997
  timestamp: 1749230354697
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-1.1.0-h5505292_3.conda
  sha256: 97e2a90342869cc122921fdff0e6be2f5c38268555c08ba5d14e1615e4637e35
  md5: 03c7865dd4dbf87b7b7d363e24c632f1
  depends:
  - __osx >=11.0
  - brotli-bin 1.1.0 h5505292_3
  - libbrotlidec 1.1.0 h5505292_3
  - libbrotlienc 1.1.0 h5505292_3
  license: MIT
  license_family: MIT
  size: 20094
  timestamp: 1749230390021
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_3.conda
  sha256: ab74fa8c3d1ca0a055226be89e99d6798c65053e2d2d3c6cb380c574972cd4a7
  md5: 58178ef8ba927229fba6d84abf62c108
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlidec 1.1.0 hb9d3cd8_3
  - libbrotlienc 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19390
  timestamp: 1749230137037
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h6e16a3a_3.conda
  sha256: 52c29e70723387e9b4265b45ee1ae5ecb2db7bcffa58cdaa22fe24b56b0505bf
  md5: a240d09be7c84cb1d33535ebd36fe422
  depends:
  - __osx >=10.13
  - libbrotlidec 1.1.0 h6e16a3a_3
  - libbrotlienc 1.1.0 h6e16a3a_3
  license: MIT
  license_family: MIT
  size: 17239
  timestamp: 1749230337410
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-bin-1.1.0-h5505292_3.conda
  sha256: 5c6a808326c3bbb6f015a57c9eb463d65f259f67154f4f06783d8829ce9239b4
  md5: cc435eb5160035fd8503e9a58036c5b5
  depends:
  - __osx >=11.0
  - libbrotlidec 1.1.0 h5505292_3
  - libbrotlienc 1.1.0 h5505292_3
  license: MIT
  license_family: MIT
  size: 17185
  timestamp: 1749230373519
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py312h2ec8cdc_3.conda
  sha256: dc27c58dc717b456eee2d57d8bc71df3f562ee49368a2351103bc8f1b67da251
  md5: a32e0c069f6c3dcac635f7b0b0dac67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  license: MIT
  license_family: MIT
  size: 351721
  timestamp: 1749230265727
- conda: https://conda.anaconda.org/conda-forge/osx-64/brotli-python-1.1.0-py312haafddd8_3.conda
  sha256: d1a8635422d99b4b7cc1b35d62d1a5c392ae0a4d74e0a44bf190916a21180ba3
  md5: 11489c0fc22f550acf63da5e7ec7304d
  depends:
  - __osx >=10.13
  - libcxx >=18
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - libbrotlicommon 1.1.0 h6e16a3a_3
  license: MIT
  license_family: MIT
  size: 367262
  timestamp: 1749230495846
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/brotli-python-1.1.0-py312hd8f9ff3_3.conda
  sha256: 35df7079768b4c51764149c42b14ccc25c4415e4365ecc06c38f74562d9e4d16
  md5: c7c728df70dc05a443f1e337c28de22d
  depends:
  - __osx >=11.0
  - libcxx >=18
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  constrains:
  - libbrotlicommon 1.1.0 h5505292_3
  license: MIT
  license_family: MIT
  size: 339365
  timestamp: 1749230606596
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/osx-64/bzip2-1.0.8-hfdf4475_7.conda
  sha256: cad153608b81fb24fc8c509357daa9ae4e49dfc535b2cb49b91e23dbd68fc3c5
  md5: 7ed4301d437b59045be7e051a0308211
  depends:
  - __osx >=10.13
  license: bzip2-1.0.6
  license_family: BSD
  size: 134188
  timestamp: 1720974491916
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/bzip2-1.0.8-h99b78c6_7.conda
  sha256: adfa71f158cbd872a36394c56c3568e6034aa55c623634b37a4836bd036e6b91
  md5: fc6948412dbbbe9a4c9ddbbcfe0a79ab
  depends:
  - __osx >=11.0
  license: bzip2-1.0.6
  license_family: BSD
  size: 122909
  timestamp: 1720974522888
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/osx-64/c-ares-1.34.5-hf13058a_0.conda
  sha256: b37f5dacfe1c59e0a207c1d65489b760dff9ddb97b8df7126ceda01692ba6e97
  md5: eafe5d9f1a8c514afe41e6e833f66dfd
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 184824
  timestamp: 1744128064511
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/c-ares-1.34.5-h5505292_0.conda
  sha256: b4bb55d0806e41ffef94d0e3f3c97531f322b3cb0ca1f7cdf8e47f62538b7a2b
  md5: f8cd1beb98240c7edb1a95883360ccfa
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 179696
  timestamp: 1744128058734
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.7.14-hbd8a1cb_0.conda
  sha256: 29defbd83c7829788358678ec996adeee252fa4d4274b7cd386c1ed73d2b201e
  md5: d16c90324aef024877d8713c0b7fea5b
  depends:
  - __unix
  license: ISC
  size: 155658
  timestamp: 1752482350666
- conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
  noarch: python
  sha256: 561e6660f26c35d137ee150187d89767c988413c978e1b712d53f27ddf70ea17
  md5: 9b347a7ec10940d3f7941ff6c460b551
  depends:
  - cached_property >=1.5.2,<1.5.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4134
  timestamp: 1615209571450
- conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
  sha256: 6dbf7a5070cc43d90a1e4c2ec0c541c69d8e30a0e25f50ce9f6e4a432e42c5d7
  md5: 576d629e47797577ab0f1b351297ef4a
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  size: 11065
  timestamp: 1615209567874
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/bioconda/linux-64/cd-hit-4.8.1-h5ca1c30_13.tar.bz2
  sha256: 8793a98a7c93b9ade5caaa85e6eeccec69d774c197b772a8132373de2d6ce8e1
  md5: 8b5beac305bcf38b77be27e0233e4076
  depends:
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libgomp
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 185148
  timestamp: 1745532039886
- conda: https://conda.anaconda.org/bioconda/osx-64/cd-hit-4.8.1-h24b48ac_13.tar.bz2
  sha256: 0fe61fc94f1ba525e00472f2b2beba15f03fd85644343f8c2b3c405866732844
  md5: 06357bdf01f61faa213504ced09c3b87
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  license: GPL-2.0-or-later
  license_family: GPL
  size: 261412
  timestamp: 1745532903412
- conda: https://conda.anaconda.org/bioconda/osx-arm64/cd-hit-4.8.1-haf7d672_13.tar.bz2
  sha256: f70d194cf4222b5c3de8cd04e846bc1b231e7f516cfb2ba4f87d6a7f2e4b590d
  md5: 3dba63c43b3ebefb6c4c8f9bbe7dcdb7
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  license: GPL-2.0-or-later
  license_family: GPL
  size: 213740
  timestamp: 1745531926568
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.7.14-pyhd8ed1ab_0.conda
  sha256: f68ee5038f37620a4fb4cdd8329c9897dce80331db8c94c3ab264a26a8c70a08
  md5: 4c07624f3faefd0bb6659fb7396cfa76
  depends:
  - python >=3.9
  license: ISC
  size: 159755
  timestamp: 1752493370797
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py312h06ac9bb_0.conda
  sha256: cba6ea83c4b0b4f5b5dc59cb19830519b28f95d7ebef7c9c5cf1c14843621457
  md5: a861504bbea4161a9170b85d4d2be840
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 294403
  timestamp: 1725560714366
- conda: https://conda.anaconda.org/conda-forge/osx-64/cffi-1.17.1-py312hf857d28_0.conda
  sha256: 94fe49aed25d84997e2630d6e776a75ee2a85bd64f258702c57faa4fe2986902
  md5: 5bbc69b8194fedc2792e451026cac34f
  depends:
  - __osx >=10.13
  - libffi >=3.4,<4.0a0
  - pycparser
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 282425
  timestamp: 1725560725144
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/cffi-1.17.1-py312h0fad829_0.conda
  sha256: 8d91a0d01358b5c3f20297c6c536c5d24ccd3e0c2ddd37f9d0593d0f0070226f
  md5: 19a5456f72f505881ba493979777b24e
  depends:
  - __osx >=11.0
  - libffi >=3.4,<4.0a0
  - pycparser
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 281206
  timestamp: 1725560813378
- conda: https://conda.anaconda.org/conda-forge/noarch/cfgv-3.3.1-pyhd8ed1ab_1.conda
  sha256: d5696636733b3c301054b948cdd793f118efacce361d9bd4afb57d5980a9064f
  md5: 57df494053e17dce2ac3a0b33e1b2a2e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 12973
  timestamp: 1734267180483
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
  sha256: 535ae5dcda8022e31c6dc063eb344c80804c537a5a04afba43a845fa6fa130f5
  md5: 40fe4284b8b5835a9073a645139f35af
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 50481
  timestamp: 1746214981991
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.3-pyhe01879c_0.conda
  sha256: 576a44729314ad9e4e5ebe055fbf48beb8116b60e58f9070278985b2b634f212
  md5: 2da13f2b299d8e1995bafbbe9689a2f7
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 14690
  timestamp: 1753453984907
- conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py312h68727a3_0.conda
  sha256: 4c8f2aa34aa031229e6f8aa18f146bce7987e26eae9c6503053722a8695ebf0c
  md5: e688276449452cdfe9f8f5d3e74c23f6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.23
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 276533
  timestamp: 1744743235779
- conda: https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.2-py312hc47a885_0.conda
  sha256: 0d1cd1d61951a3785eda1393f62a174ab089703a53b76cac58553e8442417a85
  md5: 16b4934fdd19e9d5990140cb9bd9b0d7
  depends:
  - __osx >=10.13
  - libcxx >=18
  - numpy >=1.23
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 255677
  timestamp: 1744743605195
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/contourpy-1.3.2-py312hb23fbb9_0.conda
  sha256: 39329ded9d5ea49ab230c4ecd5e7610d3c844faca05fb9385bfe76ff02cc2abd
  md5: e8108c7798046eb5b5f95cdde1bb534c
  depends:
  - __osx >=11.0
  - libcxx >=18
  - numpy >=1.23
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 245787
  timestamp: 1744743658516
- conda: https://conda.anaconda.org/conda-forge/linux-64/coverage-7.10.0-py312h8a5da7c_0.conda
  sha256: dd85469de0d62d65c456abfefd62939a28cb53704120b4f43f14f967b5123d53
  md5: 10c5a17b4546b4e236031ef8177cf9d1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tomli
  license: Apache-2.0
  size: 377204
  timestamp: 1753387756069
- conda: https://conda.anaconda.org/conda-forge/osx-64/coverage-7.10.0-py312h3d55d04_0.conda
  sha256: 2fd77045f938584065d99433073652f1160c4147b2ae7e491b77011bc806730b
  md5: 85bb94bf657c2ee1ca0b033208a3480e
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tomli
  license: Apache-2.0
  size: 375474
  timestamp: 1753387680033
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/coverage-7.10.0-py312h6daa0e5_0.conda
  sha256: 3e839d5be8df2f80df8dbba5d59dd5b65c4683449e56c7c61cfd06d939fae2ba
  md5: c5992e07bfe90eb90c59195274d9101c
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - tomli
  license: Apache-2.0
  size: 376529
  timestamp: 1753387774369
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.12.11-py312hd8ed1ab_0.conda
  noarch: generic
  sha256: 7e7bc8e73a2f3736444a8564cbece7216464c00f0bc38e604b0c792ff60d621a
  md5: e5279009e7a7f7edd3cd2880c502b3cc
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi * *_cp312
  license: Python-2.0
  size: 45852
  timestamp: 1749047748072
- conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.14.1-h332b0f4_0.conda
  sha256: 43fcf6eb083dea48fa7b3466a2683b0cc3c48b7c1875c3067a5b8c9ff18c591a
  md5: 60279087a10b4ab59a70daa838894e4b
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libcurl 8.14.1 h332b0f4_0
  - libgcc >=13
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 180334
  timestamp: 1749033158889
- conda: https://conda.anaconda.org/conda-forge/osx-64/curl-8.14.1-h5dec5d8_0.conda
  sha256: d3a1e6bf68d684efe8fb727a0665a531823a5ffb488d260bc8f442ee038d7da0
  md5: 2b76da8135d9a77e90bb858cbf7b67e2
  depends:
  - __osx >=10.13
  - krb5 >=1.21.3,<1.22.0a0
  - libcurl 8.14.1 h5dec5d8_0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 170795
  timestamp: 1749033595119
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/curl-8.14.1-h73640d1_0.conda
  sha256: fe0e505dc62632bb25bbe43ee03dc9837ca09d9bb48ac87a4e03ecbc1801e3cf
  md5: b61b498cc3913fe6e649c7e1107b6bc1
  depends:
  - __osx >=11.0
  - krb5 >=1.21.3,<1.22.0a0
  - libcurl 8.14.1 h73640d1_0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 168574
  timestamp: 1749033351449
- conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9827efa891e507a91a8a2acf64e210d2aff394e1cde432ad08e1f8c66b12293c
  md5: 44600c4667a319d67dbe0681fc0bc833
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 13399
  timestamp: 1733332563512
- conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.28-hd9c7081_0.conda
  sha256: ee09ad7610c12c7008262d713416d0b58bf365bc38584dce48950025850bdf3f
  md5: cae723309a49399d2949362f4ab5c9e4
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libntlm >=1.8,<2.0a0
  - libstdcxx >=13
  - libxcrypt >=4.4.36
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause-Attribution
  license_family: BSD
  size: 209774
  timestamp: 1750239039316
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.16-py312h8285ef7_0.conda
  sha256: ad6193b4c2771a82a8df3408d9c6174016b487fd1f7501b1618fa034c5118534
  md5: 6205bf8723b4b79275dd52ef60cf6af1
  depends:
  - python
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - python_abi 3.12.* *_cp312
  license: MIT
  size: 2856116
  timestamp: 1754523420446
- conda: https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.16-py312h2ac44ba_0.conda
  sha256: eeb94df68e7ff704a2a8ceb8bb945dc8bfbe009e900c510eb2125e2e34d98945
  md5: 5a6b041083ed03590235b65c7c8f32b4
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - python_abi 3.12.* *_cp312
  license: MIT
  size: 2760376
  timestamp: 1754523425543
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/debugpy-1.8.16-py312he360a15_0.conda
  sha256: 144542a7c6f3970a8c7012f2b0bea625e0024e809091861f688a7c0786c3e4ee
  md5: 5324a4353a78309f0cb874d1fa98e4da
  depends:
  - python
  - __osx >=11.0
  - python 3.12.* *_cpython
  - libcxx >=19
  - python_abi 3.12.* *_cp312
  license: MIT
  size: 2752346
  timestamp: 1754523441845
- conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
  sha256: c17c6b9937c08ad63cb20a26f403a3234088e57d4455600974a0ce865cb14017
  md5: 9ce473d1d1be1cc3810856a48b3fab32
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 14129
  timestamp: 1740385067843
- conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
  sha256: 9717a059677553562a8f38ff07f3b9f61727bd614f505658b0a5ecbcf8df89be
  md5: 961b3a227b437d82ad7054484cfa71b2
  depends:
  - python >=3.6
  license: PSF-2.0
  license_family: PSF
  size: 24062
  timestamp: 1615232388757
- conda: https://conda.anaconda.org/bioconda/linux-64/diamond-2.1.12-h13889ed_3.tar.bz2
  sha256: 02c4860c422d71c280f47e61a5ea1d7ca2511019b48041f3ee328879ac262e98
  md5: 7dc9f39d29b202bfdec614dee1de5d91
  depends:
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libgomp
  - libsqlite >=3.50.2,<4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: GPL-3.0-or-later
  license_family: GPL3
  size: 20477490
  timestamp: 1752629215320
- conda: https://conda.anaconda.org/bioconda/osx-64/diamond-2.1.12-h9bd2776_3.tar.bz2
  sha256: 4b4412b7a24cc53b8eaffd71b82da7ded67d1fff2ae5f8ea1469033f7c2c2da9
  md5: fa3d00f3283593c0398fda13ec139564
  depends:
  - libcxx >=18
  - libsqlite >=3.50.2,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  license: GPL-3.0-or-later
  license_family: GPL3
  size: 9712106
  timestamp: 1752631300279
- conda: https://conda.anaconda.org/bioconda/osx-arm64/diamond-2.1.12-ha27f01c_3.tar.bz2
  sha256: 38b8a272b1daf29c20798fd80a8f69830a4c221f7339a6433e3b189f8c93b108
  md5: b9bb6b728badeebd2ac6939d0c12924b
  depends:
  - libcxx >=18
  - libsqlite >=3.50.2,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  license: GPL-3.0-or-later
  license_family: GPL3
  size: 8741506
  timestamp: 1752628545864
- conda: https://conda.anaconda.org/conda-forge/noarch/distlib-0.4.0-pyhd8ed1ab_0.conda
  sha256: 6d977f0b2fc24fee21a9554389ab83070db341af6d6f09285360b2e09ef8b26e
  md5: 003b8ba0a94e2f1e117d0bd46aebc901
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 275642
  timestamp: 1752823081585
- conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
  sha256: 1bcc132fbcc13f9ad69da7aa87f60ea41de7ed4d09f3a00ff6e0e70e1c690bc2
  md5: bfd56492d8346d669010eccafe0ba058
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  size: 69544
  timestamp: 1739569648873
- conda: https://conda.anaconda.org/conda-forge/noarch/duckdb-1.3.2-hca7485f_0.conda
  sha256: 41bf20cce21c5c6038c7d97902c0abb2500e59e1bad88cc120c7e4bcb15d99fd
  md5: 0b31523d50b15f95e071712b9f803aed
  depends:
  - python-duckdb >=1.3.2,<1.3.3.0a0
  license: MIT
  license_family: MIT
  size: 8852
  timestamp: 1752087395717
- conda: https://conda.anaconda.org/bioconda/linux-64/entrez-direct-22.4-he881be0_0.tar.bz2
  sha256: 9d045676ea29460af4e7a0fe778504acce8a00e07af7906bad0e7f4242f86ea5
  md5: 0994f567f11543b7aea1afce884c2ce6
  depends:
  - wget
  license: PUBLIC DOMAIN
  size: 14686658
  timestamp: 1721746676142
- conda: https://conda.anaconda.org/bioconda/osx-64/entrez-direct-22.4-h193322a_0.tar.bz2
  sha256: c44d6c0440f7b27324cb8045d8453a9d21f087486ed53cea2d274eda01165960
  md5: 88d15c92752c1358f80caa7f40ee7274
  depends:
  - wget
  constrains:
  - __osx>=10.12
  license: PUBLIC DOMAIN
  size: 14524020
  timestamp: 1721747524581
- conda: https://conda.anaconda.org/bioconda/osx-arm64/entrez-direct-22.4-hd5f1084_0.tar.bz2
  sha256: 46efea9f61526340ae8db2c1e25666fc46a259f5a52e8d69d5ca62fedf46774d
  md5: 56c3860d52479d20d86dd78f24947e8e
  depends:
  - wget
  license: PUBLIC DOMAIN
  size: 13706791
  timestamp: 1721746465282
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
  sha256: 7510dd93b9848c6257c43fdf9ad22adf62e7aa6da5f12a6a757aed83bcfedf05
  md5: 81d30c08f9a3e556e8ca9e124b044d14
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 29652
  timestamp: 1745502200340
- conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
  sha256: de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570
  md5: 4547b39256e296bb758166893e909a7c
  depends:
  - python >=3.9
  license: Unlicense
  size: 17887
  timestamp: 1741969612334
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.59.0-py312h8a5da7c_0.conda
  sha256: ead830a4d12f26066f09b6ea54fb5c9e26a548c901063381412636db92cf7f61
  md5: 008d44a468c24a59d2e67c014fba8f12
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli
  - libgcc >=14
  - munkres
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  size: 2854951
  timestamp: **********
- conda: https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.59.0-py312h3d55d04_0.conda
  sha256: 307eddc464d1ed3d019aa98532f57ec9f294f7406779bebbec40b5dc0e19130c
  md5: 1ba85cdb649fba59ba7b65254d14bc28
  depends:
  - __osx >=10.13
  - brotli
  - munkres
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  size: 2796834
  timestamp: 1752722992690
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/fonttools-4.59.0-py312h6daa0e5_0.conda
  sha256: fb5dabc7db09891e611723622c762f625f287fc54d1f914497baf95b713513c3
  md5: 0fed8437f0bd51c23d4caa1a61fe7b3b
  depends:
  - __osx >=11.0
  - brotli
  - munkres
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  size: 2794146
  timestamp: 1752723166136
- conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
  sha256: 2509992ec2fd38ab27c7cdb42cf6cadc566a1cc0d1021a2673475d9fa87c6276
  md5: d3549fd50d450b6d9e7dddff25dd2110
  depends:
  - cached-property >=1.3.0
  - python >=3.9,<4
  license: MPL-2.0
  license_family: MOZILLA
  size: 16705
  timestamp: 1733327494780
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/osx-64/freetype-2.13.3-h694c41f_1.conda
  sha256: e2870e983889eec73fdc0d4ab27d3f6501de4750ffe32d7d0a3a287f00bc2f15
  md5: 126dba1baf5030cb6f34533718924577
  depends:
  - libfreetype 2.13.3 h694c41f_1
  - libfreetype6 2.13.3 h40dfd5c_1
  license: GPL-2.0-only OR FTL
  size: 172649
  timestamp: 1745370231293
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/freetype-2.13.3-hce30654_1.conda
  sha256: 6b63c72ea51a41d41964841404564c0729fdddd3e952e2715839fd759b7cfdfc
  md5: e684de4644067f1956a580097502bf03
  depends:
  - libfreetype 2.13.3 hce30654_1
  - libfreetype6 2.13.3 h1d14073_1
  license: GPL-2.0-only OR FTL
  size: 172220
  timestamp: 1745370149658
- conda: https://conda.anaconda.org/conda-forge/linux-64/gawk-5.3.1-hcd3d067_0.conda
  sha256: ec4ebb9444dccfcbff8a2d19b2811b48a20a58dcd08b29e3851cb930fc0f00d8
  md5: 91d4414ab699180b2b0b10b8112c5a2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libasprintf >=0.22.5,<1.0a0
  - libgcc >=13
  - libgettextpo >=0.22.5,<1.0a0
  - mpfr >=4.2.1,<5.0a0
  - readline >=8.2,<9.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 1202471
  timestamp: 1726677363710
- conda: https://conda.anaconda.org/conda-forge/osx-64/gawk-5.3.1-h0631170_0.conda
  sha256: 289022e6e9d82a88b09f6e95fac5b6e818708bd181679a009740c58004c273af
  md5: b6e1434b21225b3f8d5dfa8e7aee72b0
  depends:
  - __osx >=10.13
  - gmp >=6.3.0,<7.0a0
  - libasprintf >=0.22.5,<1.0a0
  - libgettextpo >=0.22.5,<1.0a0
  - libintl >=0.22.5,<1.0a0
  - mpfr >=4.2.1,<5.0a0
  - readline >=8.2,<9.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 1185588
  timestamp: 1726677504554
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/gawk-5.3.1-h8a92848_0.conda
  sha256: d5423de17a56f40c8e091bd471c3a3f942f0d78e89e029a7398cdf3a9fda4447
  md5: c5a8bf0b848565f613f44915bad8bc44
  depends:
  - __osx >=11.0
  - gmp >=6.3.0,<7.0a0
  - libasprintf >=0.22.5,<1.0a0
  - libgettextpo >=0.22.5,<1.0a0
  - libintl >=0.22.5,<1.0a0
  - mpfr >=4.2.1,<5.0a0
  - readline >=8.2,<9.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 1150948
  timestamp: 1726677426423
- conda: https://conda.anaconda.org/conda-forge/noarch/ghp-import-2.1.0-pyhd8ed1ab_2.conda
  sha256: 40fdf5a9d5cc7a3503cd0c33e1b90b1e6eab251aaaa74e6b965417d089809a15
  md5: 93f742fe078a7b34c29a182958d4d765
  depends:
  - python >=3.9
  - python-dateutil >=2.8.1
  license: Apache-2.0
  license_family: APACHE
  size: 16538
  timestamp: 1734344477841
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/osx-64/gmp-6.3.0-hf036a51_2.conda
  sha256: 75aa5e7a875afdcf4903b7dc98577672a3dc17b528ac217b915f9528f93c85fc
  md5: 427101d13f19c4974552a4e5b072eef1
  depends:
  - __osx >=10.13
  - libcxx >=16
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 428919
  timestamp: 1718981041839
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/gmp-6.3.0-h7bae524_2.conda
  sha256: 76e222e072d61c840f64a44e0580c2503562b009090f55aa45053bf1ccb385dd
  md5: eed7278dfbab727b56f2c0b64330814b
  depends:
  - __osx >=11.0
  - libcxx >=16
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  size: 365188
  timestamp: 1718981343258
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
  sha256: cac69f3ff7756912bbed4c28363de94f545856b35033c0b86193366b95f5317d
  md5: 951ff8d9e5536896408e89d63230b8d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  size: 98419
  timestamp: 1750079957535
- conda: https://conda.anaconda.org/conda-forge/noarch/griffe-1.8.0-pyhd8ed1ab_0.conda
  sha256: cfc2dd6154aa74a1887f1b631987148d277ef23380bff04399d0ebf1522ec6cd
  md5: f4883564820f93d9ebe128bd52e4d2a9
  depends:
  - colorama >=0.4
  - python >=3.9
  license: ISC
  size: 102454
  timestamp: 1753274050872
- conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
  sha256: f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887
  md5: 4b69232755285701bc86a5afe4d9933a
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  size: 37697
  timestamp: 1745526482242
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.3.2-hbb57e21_0.conda
  sha256: 43f55e45db9c38bb2e120056075539160a9ef6823c4838b47fcd350ba68e8793
  md5: 3fd3a7b746952a47579b8ba5dd20dbe8
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.1,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libglib >=2.84.2,<3.0a0
  - libstdcxx >=14
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1802972
  timestamp: 1753107252406
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
  sha256: 04d49cb3c42714ce533a8553986e1642d0549a05dc5cc48e0d43ff5be6679a5b
  md5: 4f14640d58e2cc0aa0819d9d8ba125bb
  depends:
  - python >=3.9
  - h11 >=0.16
  - h2 >=3,<5
  - sniffio 1.*
  - anyio >=4.0,<5.0
  - certifi
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 49483
  timestamp: 1745602916758
- conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
  sha256: cd0f1de3697b252df95f98383e9edb1d00386bfdd03fdf607fa42fe5fcb09950
  md5: d6989ead454181f4f9bc987d3dc4e285
  depends:
  - anyio
  - certifi
  - httpcore 1.*
  - idna
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 63082
  timestamp: 1733663449209
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/osx-64/icu-75.1-h120a0e1_0.conda
  sha256: 2e64307532f482a0929412976c8450c719d558ba20c0962832132fd0d07ba7a7
  md5: d68d48a3060eb5abdc1cdc8e2a3a5966
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 11761697
  timestamp: 1720853679409
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/icu-75.1-hfee45f7_0.conda
  sha256: 9ba12c93406f3df5ab0a43db8a4b4ef67a5871dfd401010fbe29b218b2cbe620
  md5: 5eb22c1d7b3fc4abb50d92d621583137
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 11857802
  timestamp: 1720853997952
- conda: https://conda.anaconda.org/conda-forge/noarch/identify-2.6.12-pyhd8ed1ab_0.conda
  sha256: 4debbae49a183d61f0747a5f594fca2bf5121e8508a52116f50ccd0eb2f7bb55
  md5: 84463b10c1eb198541cd54125c7efe90
  depends:
  - python >=3.9
  - ukkonen
  license: MIT
  license_family: MIT
  size: 78926
  timestamp: 1748049754416
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
  sha256: 0ec8f4d02053cd03b0f3e63168316530949484f80e16f5e2fb199a1d117a89ca
  md5: 6837f3eff7dcea42ecd714ce1ac2b108
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11474
  timestamp: 1733223232820
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh82676e8_0.conda
  sha256: cfc2c4e31dfedbb3d124d0055f55fda4694538fb790d52cd1b37af5312833e36
  md5: b0cc25825ce9212b8bee37829abad4d6
  depends:
  - __linux
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=8.0.0
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio >=1.4
  - packaging >=22
  - psutil >=5.7
  - python >=3.9
  - pyzmq >=25
  - tornado >=6.2
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 121367
  timestamp: 1754352984703
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.30.1-pyh92f572d_0.conda
  sha256: ec80ed5f68c96dd46ff1b533b28d2094b6f07e2ec8115c8c60803920fdd6eb13
  md5: f208c1a85786e617a91329fa5201168c
  depends:
  - __osx
  - appnope
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=8.0.0
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio >=1.4
  - packaging >=22
  - psutil >=5.7
  - python >=3.9
  - pyzmq >=25
  - tornado >=6.2
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  size: 121397
  timestamp: 1754353050327
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython-9.4.0-pyhfa0c392_0.conda
  sha256: ff5138bf6071ca01d84e1329f6baa96f0723df6fe183cfa1ab3ebc96240e6d8f
  md5: cb7706b10f35e7507917cefa0978a66d
  depends:
  - __unix
  - pexpect >4.3
  - decorator
  - exceptiongroup
  - ipython_pygments_lexers
  - jedi >=0.16
  - matplotlib-inline
  - pickleshare
  - prompt-toolkit >=3.0.41,<3.1.0
  - pygments >=2.4.0
  - python >=3.11
  - stack_data
  - traitlets >=5.13.0
  - typing_extensions >=4.6
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 628259
  timestamp: 1751465044469
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0.conda
  sha256: 894682a42a7d659ae12878dbcb274516a7031bbea9104e92f8e88c1f2765a104
  md5: bd80ba060603cc228d9d81c257093119
  depends:
  - pygments
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 13993
  timestamp: 1737123723464
- conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
  sha256: fd496e7d48403246f534c5eec09fc1e63ac7beb1fa06541d6ba71f56b30cf29b
  md5: 7c9449eac5975ef2d7753da262a72707
  depends:
  - comm >=0.1.3
  - ipython >=6.1.0
  - jupyterlab_widgets >=3.0.15,<3.1.0
  - python >=3.9
  - traitlets >=4.3.1
  - widgetsnbextension >=4.0.14,<4.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 114557
  timestamp: 1746454722402
- conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
  sha256: 08e838d29c134a7684bca0468401d26840f41c92267c4126d7b43a6b533b0aed
  md5: 0b0154421989637d424ccf0f104be51a
  depends:
  - arrow >=0.15.0
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 19832
  timestamp: 1733493720346
- conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
  sha256: 92c4d217e2dc68983f724aa983cca5464dcb929c566627b26a2511159667dba8
  md5: a4f4c5dc9b80bc50e0d3dc4e6e8f1bd9
  depends:
  - parso >=0.8.3,<0.9.0
  - python >=3.9
  license: Apache-2.0 AND MIT
  size: 843646
  timestamp: 1733300981994
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 112714
  timestamp: 1741263433881
- conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
  sha256: e5a4eca9a5d8adfaa3d51e24eefd1a6d560cb3b33a7e1eee13e410bec457b7ed
  md5: fb1c14694de51a476ce8636d92b6f42c
  depends:
  - python >=3.9
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 224437
  timestamp: 1748019237972
- conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
  sha256: 889e2a49de796475b5a4bc57d0ba7f4606b368ee2098e353a6d9a14b0e2c6393
  md5: 56275442557b3b45752c10980abfe2db
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 34114
  timestamp: 1743722170015
- conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py312h7900ff3_1.conda
  sha256: 76ccb7bffc7761d1d3133ffbe1f7f1710a0f0d9aaa9f7ea522652e799f3601f4
  md5: 6b51f7459ea4073eeb5057207e2e1e3d
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 17277
  timestamp: 1725303032027
- conda: https://conda.anaconda.org/conda-forge/osx-64/jsonpointer-3.0.0-py312hb401068_1.conda
  sha256: 52fcb1db44a935bba26988cc17247a0f71a8ad2fbc2b717274a8c8940856ee0d
  md5: 5dcf96bca4649d496d818a0f5cfb962e
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 17560
  timestamp: 1725303027769
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/jsonpointer-3.0.0-py312h81bd7bf_1.conda
  sha256: f6fb3734e967d1cd0cde32844ee952809f6c0a49895da7ec1c8cfdf97739b947
  md5: 80f403c03290e1662be03e026fb5f8ab
  depends:
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 17865
  timestamp: 1725303130815
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.25.0-pyhe01879c_0.conda
  sha256: 87ba7cf3a65c8e8d1005368b9aee3f49e295115381b7a0b180e56f7b68b5975f
  md5: c6e3fd94e058dba67d917f38a11b50ab
  depends:
  - attrs >=22.2.0
  - jsonschema-specifications >=2023.3.6
  - python >=3.9
  - referencing >=0.28.4
  - rpds-py >=0.7.1
  - python
  license: MIT
  license_family: MIT
  size: 81493
  timestamp: 1752925388185
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
  sha256: 66fbad7480f163509deec8bd028cd3ea68e58022982c838683586829f63f3efa
  md5: 41ff526b1083fde51fbdc93f29282e0e
  depends:
  - python >=3.9
  - referencing >=0.31.0
  - python
  license: MIT
  license_family: MIT
  size: 19168
  timestamp: 1745424244298
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.25.0-he01879c_0.conda
  sha256: 72604d07afaddf2156e61d128256d686aee4a7bdc06e235d7be352955de7527a
  md5: f4c7afaf838ab5bb1c4e73eb3095fb26
  depends:
  - jsonschema >=4.25.0,<4.25.1.0a0
  - fqdn
  - idna
  - isoduration
  - jsonpointer >1.13
  - rfc3339-validator
  - rfc3986-validator >0.1.0
  - rfc3987-syntax >=1.1.0
  - uri-template
  - webcolors >=24.6.0
  license: MIT
  license_family: MIT
  size: 4744
  timestamp: 1752925388185
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.6-pyhe01879c_0.conda
  sha256: 6f2d6c5983e013af68e7e1d7082cc46b11f55e28147bd0a72a44488972ed90a3
  md5: 7129ed52335cc7164baf4d6508a3f233
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_server >=1.1.2
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 58416
  timestamp: 1752935193718
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.8.1-pyh31011fe_0.conda
  sha256: 56a7a7e907f15cca8c4f9b0c99488276d4cb10821d2d15df9245662184872e81
  md5: b7d89d860ebcda28a5303526cdee68ab
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  size: 59562
  timestamp: 1748333186063
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
  sha256: 37e6ac3ccf7afcc730c3b93cb91a13b9ae827fd306f35dd28f958a74a14878b5
  md5: f56000b36f09ab7533877e695e4e8cb0
  depends:
  - jsonschema-with-format-nongpl >=4.18.0
  - packaging
  - python >=3.9
  - python-json-logger >=2.0.4
  - pyyaml >=5.3
  - referencing
  - rfc3339-validator
  - rfc3986-validator >=0.1.1
  - traitlets >=5.3
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 23647
  timestamp: 1738765986736
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
  sha256: 0082fb6f0afaf872affee4cde3b210f7f7497a5fb47f2944ab638fef0f0e2e77
  md5: f062e04d7cd585c937acbf194dceec36
  depends:
  - anyio >=3.1.0
  - argon2-cffi >=21.1
  - jinja2 >=3.0.3
  - jupyter_client >=7.4.4
  - jupyter_core >=4.12,!=5.0.*
  - jupyter_events >=0.11.0
  - jupyter_server_terminals >=0.4.4
  - nbconvert-core >=6.4.4
  - nbformat >=5.3.0
  - overrides >=5.0
  - packaging >=22.0
  - prometheus_client >=0.9
  - python >=3.9
  - pyzmq >=24
  - send2trash >=1.8.2
  - terminado >=0.8.3
  - tornado >=6.2.0
  - traitlets >=5.6.0
  - websocket-client >=1.7
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 344376
  timestamp: 1747083217715
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
  sha256: 0890fc79422191bc29edf17d7b42cff44ba254aa225d31eb30819f8772b775b8
  md5: 2d983ff1b82a1ccb6f2e9d8784bdd6bd
  depends:
  - python >=3.9
  - terminado >=0.8.3
  license: BSD-3-Clause
  license_family: BSD
  size: 19711
  timestamp: 1733428049134
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.5-pyhd8ed1ab_0.conda
  sha256: 2013c2dd13bc773167e1ad11ae885b550c0297d030e2107bdc303243ff05d3f2
  md5: ad6bbe770780dcf9cf55d724c5a213fd
  depends:
  - async-lru >=1.0.0
  - httpx >=0.25.0
  - importlib-metadata >=4.8.3
  - ipykernel >=6.5.0
  - jinja2 >=3.0.3
  - jupyter-lsp >=2.0.0
  - jupyter_core
  - jupyter_server >=2.4.0,<3
  - jupyterlab_server >=2.27.1,<3
  - notebook-shim >=0.2
  - packaging
  - python >=3.9
  - setuptools >=41.1.0
  - tomli >=1.2.2
  - tornado >=6.2.0
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  size: 8074534
  timestamp: 1753022530771
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
  sha256: dc24b900742fdaf1e077d9a3458fd865711de80bca95fe3c6d46610c532c6ef0
  md5: fd312693df06da3578383232528c468d
  depends:
  - pygments >=2.4.1,<3
  - python >=3.9
  constrains:
  - jupyterlab >=4.0.8,<5.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 18711
  timestamp: 1733328194037
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
  sha256: d03d0b7e23fa56d322993bc9786b3a43b88ccc26e58b77c756619a921ab30e86
  md5: 9dc4b2b0f41f0de41d27f3293e319357
  depends:
  - babel >=2.10
  - importlib-metadata >=4.8.3
  - jinja2 >=3.0.3
  - json5 >=0.9.0
  - jsonschema >=4.18
  - jupyter_server >=1.21,<3
  - packaging >=21.3
  - python >=3.9
  - requests >=2.31
  constrains:
  - openapi-core >=0.18.0,<0.19.0
  license: BSD-3-Clause
  license_family: BSD
  size: 49449
  timestamp: 1733599666357
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
  sha256: 6214d345861b106076e7cb38b59761b24cd340c09e3f787e4e4992036ca3cd7e
  md5: ad100d215fad890ab0ee10418f36876f
  depends:
  - python >=3.9
  constrains:
  - jupyterlab >=3,<5
  license: BSD-3-Clause
  license_family: BSD
  size: 189133
  timestamp: 1746450926999
- conda: https://conda.anaconda.org/bioconda/linux-64/k8-1.2-he8db53b_6.tar.bz2
  sha256: 35143205ef4684f417f047c649aea110e0ceb1d6891001511dbceb2dea08be3d
  md5: 6c44663f234185cc54d7a90c4d7a555a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - sysroot_linux-64 >=2.17
  license: MIT
  license_family: MIT
  size: 7535090
  timestamp: 1748942479223
- conda: https://conda.anaconda.org/bioconda/osx-64/k8-1.2-h2ec61ea_6.tar.bz2
  sha256: 596c13ecad0e679a907c229b9fcd07f649235ad25abbf783e8910a449ec60f2d
  md5: 917525300d56b7a7ddac9e1bde2dd4ba
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 8484801
  timestamp: 1748948933576
- conda: https://conda.anaconda.org/bioconda/osx-arm64/k8-1.2-hda5e58c_6.tar.bz2
  sha256: 71a5f0bcdb892760556b741fcba8b52d9824a414097cc5e877f36e89b093e3c0
  md5: 7564d9ace7ab6eed5f972bf2a799b166
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 8068855
  timestamp: 1748940155242
- conda: https://conda.anaconda.org/conda-forge/noarch/kernel-headers_linux-64-4.18.0-he073ed8_8.conda
  sha256: 305c22a251db227679343fd73bfde121e555d466af86e537847f4c8b9436be0d
  md5: ff007ab0f0fdc53d245972bba8a6d40c
  constrains:
  - sysroot_linux-64 ==2.28
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later
  license_family: GPL
  size: 1272697
  timestamp: 1752669126073
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.8-py312h68727a3_1.conda
  sha256: 34814cea4b92d17237211769f2ec5b739a328849b152a2f5736183c52d48cafc
  md5: a8ea818e46addfa842348701a9dbe8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 72166
  timestamp: 1751493973594
- conda: https://conda.anaconda.org/conda-forge/osx-64/kiwisolver-1.4.8-py312hc47a885_1.conda
  sha256: f9c1706f34df7fdba091eebb8e24d5d49a275bf9b0a872235eaa6ce36381533c
  md5: b7ae5fe6702b5d6bd6a540fa1b6f2b8b
  depends:
  - __osx >=10.13
  - libcxx >=18
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 63367
  timestamp: 1751494217267
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/kiwisolver-1.4.8-py312hb23fbb9_1.conda
  sha256: f75e00ed3fe2db218fa58d37148c437c5852ce0a4e3f08563e24ab98045ddc5e
  md5: aebb58801a162a0a0ed75df72a9bbeb1
  depends:
  - __osx >=11.0
  - libcxx >=18
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 61937
  timestamp: 1751494129774
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/osx-64/krb5-1.21.3-h37d8d59_0.conda
  sha256: 83b52685a4ce542772f0892a0f05764ac69d57187975579a0835ff255ae3ef9c
  md5: d4765c524b1d91567886bde656fb514b
  depends:
  - __osx >=10.13
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1185323
  timestamp: 1719463492984
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/krb5-1.21.3-h237132a_0.conda
  sha256: 4442f957c3c77d69d9da3521268cad5d54c9033f1a73f99cde0a3658937b159b
  md5: c6dc8a0fdec13a0565936655c33069a1
  depends:
  - __osx >=11.0
  - libcxx >=16
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  size: 1155530
  timestamp: 1719463474401
- conda: https://conda.anaconda.org/conda-forge/noarch/lark-1.2.2-pyhd8ed1ab_1.conda
  sha256: 637a9c32e15a4333f1f9c91e0a506dbab4a6dab7ee83e126951159c916c81c99
  md5: 3a8063b25e603999188ed4bbf3485404
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 92093
  timestamp: 1734709450256
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/osx-64/lcms2-2.17-h72f5680_0.conda
  sha256: bcb81543e49ff23e18dea79ef322ab44b8189fb11141b1af99d058503233a5fc
  md5: bf210d0c63f2afb9e414a858b79f0eaa
  depends:
  - __osx >=10.13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 226001
  timestamp: 1739161050843
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lcms2-2.17-h7eeda09_0.conda
  sha256: 310a62c2f074ebd5aa43b3cd4b00d46385ce680fa2132ecee255a200e2d2f15f
  md5: 92a61fd30b19ebd5c1621a5bfe6d8b5f
  depends:
  - __osx >=11.0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  size: 212125
  timestamp: 1739161108467
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.44-h1423503_1.conda
  sha256: 1a620f27d79217c1295049ba214c2f80372062fd251b569e9873d4a953d27554
  md5: 0be7c6e070c19105f966d3758448d018
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.44
  license: GPL-3.0-only
  license_family: GPL
  size: 676044
  timestamp: 1752032747103
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/osx-64/lerc-4.0.0-hcca01a6_1.conda
  sha256: cc1f1d7c30aa29da4474ec84026ec1032a8df1d7ec93f4af3b98bb793d01184e
  md5: 21f765ced1a0ef4070df53cb425e1967
  depends:
  - __osx >=10.13
  - libcxx >=18
  license: Apache-2.0
  license_family: Apache
  size: 248882
  timestamp: 1745264331196
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/lerc-4.0.0-hd64df32_1.conda
  sha256: 12361697f8ffc9968907d1a7b5830e34c670e4a59b638117a2cdfed8f63a38f8
  md5: a74332d9b60b62905e3d30709df08bf1
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: Apache-2.0
  license_family: Apache
  size: 188306
  timestamp: 1745264362794
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.25.1-h3f43e3d_1.conda
  sha256: cb728a2a95557bb6a5184be2b8be83a6f2083000d0c7eff4ad5bbe5792133541
  md5: 3b0d184bc9404516d418d4509e418bdc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  license: LGPL-2.1-or-later
  size: 53582
  timestamp: 1753342901341
- conda: https://conda.anaconda.org/conda-forge/osx-64/libasprintf-0.25.1-h3184127_1.conda
  sha256: 44e703d8fe739a71e9f7b89d04b56ccfaf488989f7712256bc0fcaf101e796a4
  md5: 37398594a1ede86a90c0afac95e1ffea
  depends:
  - __osx >=10.13
  - libcxx >=19
  license: LGPL-2.1-or-later
  size: 51955
  timestamp: 1753343931663
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libasprintf-0.25.1-h493aca8_0.conda
  sha256: 7265547424e978ea596f51cc8e7b81638fb1c660b743e98cc4deb690d9d524ab
  md5: 0deb80a2d6097c5fb98b495370b2435b
  depends:
  - __osx >=11.0
  - libcxx >=18
  license: LGPL-2.1-or-later
  size: 52316
  timestamp: 1751558366611
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-32_h59b9bed_openblas.conda
  build_number: 32
  sha256: 1540bf739feb446ff71163923e7f044e867d163c50b605c8b421c55ff39aa338
  md5: 2af9f3d5c2e39f417ce040f5a35c40c6
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - libcblas   3.9.0   32*_openblas
  - mkl <2025
  - liblapacke 3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17330
  timestamp: 1750388798074
- conda: https://conda.anaconda.org/conda-forge/osx-64/libblas-3.9.0-32_h7f60823_openblas.conda
  build_number: 32
  sha256: e441fcc46858a9a073e4344c80e267aee3b95ec01b02e37205c36be79eec0694
  md5: 0f7197e3b4ecfa8aa24a371c3eaabb8a
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - liblapack  3.9.0   32*_openblas
  - blas 2.132   openblas
  - mkl <2025
  - liblapacke 3.9.0   32*_openblas
  - libcblas   3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17571
  timestamp: 1750389030403
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libblas-3.9.0-32_h10e41b3_openblas.conda
  build_number: 32
  sha256: 2775472dd81d43dc20804b484028560bfecd5ab4779e39f1fb95684da3ff2029
  md5: d4a1732d2b330c9d5d4be16438a0ac78
  depends:
  - libopenblas >=0.3.30,<0.3.31.0a0
  - libopenblas >=0.3.30,<1.0a0
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - mkl <2025
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17520
  timestamp: 1750388963178
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_3.conda
  sha256: 462a8ed6a7bb9c5af829ec4b90aab322f8bcd9d8987f793e6986ea873bbd05cf
  md5: cb98af5db26e3f482bebb80ce9d947d3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 69233
  timestamp: 1749230099545
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlicommon-1.1.0-h6e16a3a_3.conda
  sha256: 23952b1dc3cd8be168995da2d7cc719dac4f2ec5d478ba4c65801681da6f9f52
  md5: ec21ca03bcc08f89b7e88627ae787eaf
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 67817
  timestamp: 1749230267706
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlicommon-1.1.0-h5505292_3.conda
  sha256: 0e9c196ad8569ca199ea05103707cde0ae3c7e97d0cdf0417d873148ea9ad640
  md5: fbc4d83775515e433ef22c058768b84d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 68972
  timestamp: 1749230317752
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_3.conda
  sha256: 3eb27c1a589cbfd83731be7c3f19d6d679c7a444c3ba19db6ad8bf49172f3d83
  md5: 1c6eecffad553bde44c5238770cfb7da
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 33148
  timestamp: 1749230111397
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlidec-1.1.0-h6e16a3a_3.conda
  sha256: 499374a97637e4c6da0403ced7c9860d25305c6cb92c70dded738134c4973c67
  md5: 71d03e5e44801782faff90c455b3e69a
  depends:
  - __osx >=10.13
  - libbrotlicommon 1.1.0 h6e16a3a_3
  license: MIT
  license_family: MIT
  size: 30627
  timestamp: 1749230291245
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlidec-1.1.0-h5505292_3.conda
  sha256: d888c228e7d4f0f2303538f6a9705498c81d56fedaab7811e1186cb6e24d689b
  md5: 01c4b35a1c4b94b60801f189f1ac6ee3
  depends:
  - __osx >=11.0
  - libbrotlicommon 1.1.0 h5505292_3
  license: MIT
  license_family: MIT
  size: 29249
  timestamp: 1749230338861
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_3.conda
  sha256: 76e8492b0b0a0d222bfd6081cae30612aa9915e4309396fdca936528ccf314b7
  md5: 3facafe58f3858eb95527c7d3a3fc578
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_3
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 282657
  timestamp: 1749230124839
- conda: https://conda.anaconda.org/conda-forge/osx-64/libbrotlienc-1.1.0-h6e16a3a_3.conda
  sha256: e6d7a42fe87a23df03c482c885e428cc965d1628f18e5cee47575f6216c7fbc5
  md5: 94c0090989db51216f40558958a3dd40
  depends:
  - __osx >=10.13
  - libbrotlicommon 1.1.0 h6e16a3a_3
  license: MIT
  license_family: MIT
  size: 295250
  timestamp: 1749230310752
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libbrotlienc-1.1.0-h5505292_3.conda
  sha256: 0734a54db818ddfdfbf388fa53c5036a06bbe17de14005f33215d865d51d8a5e
  md5: 1ce5e315293309b5bf6778037375fb08
  depends:
  - __osx >=11.0
  - libbrotlicommon 1.1.0 h5505292_3
  license: MIT
  license_family: MIT
  size: 274404
  timestamp: 1749230355483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-32_he106b2a_openblas.conda
  build_number: 32
  sha256: 92a001fc181e6abe4f4a672b81d9413ca2f22609f8a95327dfcc6eee593ffeb9
  md5: 3d3f9355e52f269cd8bc2c440d8a5263
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17308
  timestamp: 1750388809353
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-32_hff6cab4_openblas.conda
  build_number: 32
  sha256: 745f6dd420389809c333734df6edc99d75caa3633e4778158c7549c6844af440
  md5: 2c1e774d4546cf542eaee5781eb8940b
  depends:
  - libblas 3.9.0 32_h7f60823_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17574
  timestamp: 1750389040732
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcblas-3.9.0-32_hb3479ef_openblas.conda
  build_number: 32
  sha256: 25d46ace14c3ac45d4aa18b5f7a0d3d30cec422297e900f8b97a66334232061c
  md5: d8e8ba717ae863b13a7495221f2b5a71
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - liblapack  3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17485
  timestamp: 1750388970626
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.8-default_hddf928d_0.conda
  sha256: 202742a287db5889ae5511fab24b4aff40f0c515476c1ea130ff56fae4dd565a
  md5: b939740734ad5a8e8f6c942374dee68d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libllvm20 >=20.1.8,<20.2.0a0
  - libstdcxx >=14
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 21250278
  timestamp: 1752223579291
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-20.1.8-default_ha444ac7_0.conda
  sha256: 39fdf9616df5dd13dee881fc19e8f9100db2319e121d9b673a3fc6a0c76743a3
  md5: 783f9cdcb0255ed00e3f1be22e16de40
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libllvm20 >=20.1.8,<20.2.0a0
  - libstdcxx >=14
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 12353158
  timestamp: 1752223792409
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-hb8b1518_5.conda
  sha256: cb83980c57e311783ee831832eb2c20ecb41e7dee6e86e8b70b8cef0e43eab55
  md5: d4a250da4737ee127fb1fa6452a9002e
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: Apache-2.0
  license_family: Apache
  size: 4523621
  timestamp: 1749905341688
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.14.1-h332b0f4_0.conda
  sha256: b6c5cf340a4f80d70d64b3a29a7d9885a5918d16a5cb952022820e6d3e79dc8b
  md5: 45f6713cb00f124af300342512219182
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 449910
  timestamp: 1749033146806
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcurl-8.14.1-h5dec5d8_0.conda
  sha256: ca0d8d12056227d6b47122cfb6d68fc5a3a0c6ab75a0e908542954fc5f84506c
  md5: 8738cd19972c3599400404882ddfbc24
  depends:
  - __osx >=10.13
  - krb5 >=1.21.3,<1.22.0a0
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 424040
  timestamp: 1749033558114
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcurl-8.14.1-h73640d1_0.conda
  sha256: 0055b68137309db41ec34c938d95aec71d1f81bd9d998d5be18f32320c3ccba0
  md5: 1af57c823803941dfc97305248a56d57
  depends:
  - __osx >=11.0
  - krb5 >=1.21.3,<1.22.0a0
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  size: 403456
  timestamp: 1749033320430
- conda: https://conda.anaconda.org/conda-forge/osx-64/libcxx-20.1.8-h3d58e20_1.conda
  sha256: 9643d6c5a94499cddb5ae1bccc4f78aef8cfd77bcf6b37ad325bc7232a8a870f
  md5: d2db320b940047515f7a27f870984fe7
  depends:
  - __osx >=10.13
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 564830
  timestamp: 1752814841086
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libcxx-20.1.8-hf598326_1.conda
  sha256: 119b3ac75cb1ea29981e5053c2cb10d5f0b06fcc81b486cb7281f160daf673a1
  md5: a69ef3239d3268ef8602c7a7823fd982
  depends:
  - __osx >=11.0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 568267
  timestamp: 1752814881595
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/osx-64/libdeflate-1.24-hcc1b750_0.conda
  sha256: 2733a4adf53daca1aa4f41fe901f0f8ee9e4c509abd23ffcd7660013772d6f45
  md5: f0a46c359722a3e84deb05cd4072d153
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 69751
  timestamp: 1747040526774
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libdeflate-1.24-h5773f1b_0.conda
  sha256: 417d52b19c679e1881cce3f01cad3a2d542098fa2d6df5485aac40f01aede4d1
  md5: 3baf58a5a87e7c2f4d243ce2f8f2fe5c
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 54790
  timestamp: 1747040549847
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
  sha256: f53458db897b93b4a81a6dbfd7915ed8fa4a54951f97c698dde6faa028aadfd2
  md5: 4c0ab57463117fbb8df85268415082f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  size: 246161
  timestamp: 1749904704373
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda
  sha256: 6cc49785940a99e6a6b8c6edbb15f44c2dd6c789d9c283e5ee7bdfedd50b4cd6
  md5: 1f4ed31220402fcddc083b4bff406868
  depends:
  - ncurses
  - __osx >=10.13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 115563
  timestamp: 1738479554273
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libedit-3.1.20250104-pl5321hafb1f1b_0.conda
  sha256: 66aa216a403de0bb0c1340a88d1a06adaff66bae2cfd196731aa24db9859d631
  md5: 44083d2d2c2025afca315c7a172eab2b
  depends:
  - ncurses
  - __osx >=11.0
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 107691
  timestamp: 1738479560845
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/osx-64/libev-4.33-h10d778d_2.conda
  sha256: 0d238488564a7992942aa165ff994eca540f687753b4f0998b29b4e4d030ff43
  md5: 899db79329439820b7e8f8de41bca902
  license: BSD-2-Clause
  license_family: BSD
  size: 106663
  timestamp: 1702146352558
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libev-4.33-h93a5062_2.conda
  sha256: 95cecb3902fbe0399c3a7e67a5bed1db813e5ab0e22f4023a5e0f722f2cc214f
  md5: 36d33e440c31857372a72137f78bacf5
  license: BSD-2-Clause
  license_family: BSD
  size: 107458
  timestamp: 1702146414478
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.1-hecca717_0.conda
  sha256: da2080da8f0288b95dd86765c801c6e166c4619b910b11f9a8446fb852438dc2
  md5: 4211416ecba1866fab0c6470986c22d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  size: 74811
  timestamp: 1752719572741
- conda: https://conda.anaconda.org/conda-forge/osx-64/libexpat-2.7.1-h21dd04a_0.conda
  sha256: 689862313571b62ee77ee01729dc093f2bf25a2f99415fcfe51d3a6cd31cce7b
  md5: 9fdeae0b7edda62e989557d645769515
  depends:
  - __osx >=10.13
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  size: 72450
  timestamp: 1752719744781
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libexpat-2.7.1-hec049ff_0.conda
  sha256: 8fbb17a56f51e7113ed511c5787e0dec0d4b10ef9df921c4fd1cccca0458f648
  md5: b1ca5f21335782f71a8bd69bdc093f67
  depends:
  - __osx >=11.0
  constrains:
  - expat 2.7.1.*
  license: MIT
  license_family: MIT
  size: 65971
  timestamp: 1752719657566
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/osx-64/libffi-3.4.6-h281671d_1.conda
  sha256: 6394b1bc67c64a21a5cc73d1736d1d4193a64515152e861785c44d2cfc49edf3
  md5: 4ca9ea59839a9ca8df84170fab4ceb41
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 51216
  timestamp: 1743434595269
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libffi-3.4.6-h1da3d7d_1.conda
  sha256: c6a530924a9b14e193ea9adfe92843de2a806d1b7dbfd341546ece9653129e60
  md5: c215a60c2935b517dcda8cad4705734d
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 39839
  timestamp: 1743434670405
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.13.3-h694c41f_1.conda
  sha256: afe0e2396844c8cfdd6256ac84cabc9af823b1727f704c137b030b85839537a6
  md5: 07c8d3fbbe907f32014b121834b36dd5
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7805
  timestamp: 1745370212559
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype-2.13.3-hce30654_1.conda
  sha256: 1f8c16703fe333cdc2639f7cdaf677ac2120843453222944a7c6c85ec342903c
  md5: d06282e08e55b752627a707d58779b8f
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 7813
  timestamp: 1745370144506
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.13.3-h40dfd5c_1.conda
  sha256: 058165962aa64fc5a6955593212c0e1ea42ca6d6dba60ee61dff612d4c3818d7
  md5: c76e6f421a0e95c282142f820835e186
  depends:
  - __osx >=10.13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 357654
  timestamp: 1745370210187
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libfreetype6-2.13.3-h1d14073_1.conda
  sha256: c278df049b1a071841aa0aca140a338d087ea594e07dcf8a871d2cfe0e330e75
  md5: b163d446c55872ef60530231879908b9
  depends:
  - __osx >=11.0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  size: 333529
  timestamp: 1745370142848
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.25.1-h3f43e3d_1.conda
  sha256: 50a9e9815cf3f5bce1b8c5161c0899cc5b6c6052d6d73a4c27f749119e607100
  md5: 2f4de899028319b27eb7a4023be5dfd2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libiconv >=1.18,<2.0a0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 188293
  timestamp: 1753342911214
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgettextpo-0.25.1-h3184127_1.conda
  sha256: 0509a41da5179727d24092020bc3d4addcb24a421c2e889d32a4035652fab2cf
  md5: 711bff88af3b00283f7d8f32aff82e6a
  depends:
  - __osx >=10.13
  - libiconv >=1.18,<2.0a0
  - libintl 0.25.1 h3184127_1
  license: GPL-3.0-or-later
  license_family: GPL
  size: 198908
  timestamp: 1753344027461
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgettextpo-0.25.1-h493aca8_0.conda
  sha256: 3ba35ff26b3b9573b5df5b9bbec5c61476157ec3a9f12c698e2a9350cd4338fd
  md5: 98acd9989d0d8d5914ccc86dceb6c6c2
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  - libintl 0.25.1 h493aca8_0
  license: GPL-3.0-or-later
  license_family: GPL
  size: 183091
  timestamp: 1751558452316
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_3.conda
  sha256: 77dd1f1efd327e6991e87f09c7c97c4ae1cfbe59d9485c41d339d6391ac9c183
  md5: bfbca721fd33188ef923dfe9ba172f29
  depends:
  - libgfortran5 15.1.0 hcea5267_3
  constrains:
  - libgfortran-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29057
  timestamp: 1750808257258
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran-5.0.0-14_2_0_h51e75f0_103.conda
  sha256: 124dcd89508bd16f562d9d3ce6a906336a7f18e963cd14f2877431adee14028e
  md5: 090b3c9ae1282c8f9b394ac9e4773b10
  depends:
  - libgfortran5 14.2.0 h51e75f0_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 156202
  timestamp: 1743862427451
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran-5.0.0-14_2_0_h6c33f7e_103.conda
  sha256: 8628746a8ecd311f1c0d14bb4f527c18686251538f7164982ccbe3b772de58b5
  md5: 044a210bc1d5b8367857755665157413
  depends:
  - libgfortran5 14.2.0 h6c33f7e_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 156291
  timestamp: 1743863532821
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_3.conda
  sha256: eea6c3cf22ad739c279b4d665e6cf20f8081f483b26a96ddd67d4df3c88dfa0a
  md5: 530566b68c3b8ce7eec4cd047eae19fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 1565627
  timestamp: 1750808236464
- conda: https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-14.2.0-h51e75f0_103.conda
  sha256: d2ac5e09587e5b21b7bb5795d24f33257e44320749c125448611211088ef8795
  md5: 6183f7e9cd1e7ba20118ff0ca20a05e5
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 5.0.0 14_2_0_*_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 1225013
  timestamp: 1743862382377
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libgfortran5-14.2.0-h6c33f7e_103.conda
  sha256: 8599453990bd3a449013f5fa3d72302f1c68f0680622d419c3f751ff49f01f17
  md5: 69806c1e957069f1d515830dcc9f6cbb
  depends:
  - llvm-openmp >=8.0.0
  constrains:
  - libgfortran 5.0.0 14_2_0_*_103
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 806566
  timestamp: 1743863491726
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
  sha256: a6b5cf4d443044bc9a0293dd12ca2015f0ebe5edfdc9c4abdde0b9947f9eb7bd
  md5: 072ab14a02164b7c0c089055368ff776
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  size: 3955066
  timestamp: 1747836671118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
  sha256: 43710ab4de0cd7ff8467abff8d11e7bb0e36569df04ce1c099d48601818f11d1
  md5: 3cd1a7238a0dd3d0860fdefc496cc854
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 447068
  timestamp: 1750808138400
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/osx-64/libiconv-1.18-h4b5e92a_1.conda
  sha256: c2a9c65a245c7bcb8c17c94dd716dad2d42b7c98e0c17cc5553a5c60242c4dda
  md5: 6283140d7b2b55b6b095af939b71b13f
  depends:
  - __osx >=10.13
  license: LGPL-2.1-only
  size: 669052
  timestamp: 1740128415026
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libiconv-1.18-hfe07756_1.conda
  sha256: d30780d24bf3a30b4f116fca74dedb4199b34d500fe6c52cced5f8cc1e926f03
  md5: 450e6bdc0c7d986acf7b8443dce87111
  depends:
  - __osx >=11.0
  license: LGPL-2.1-only
  size: 681804
  timestamp: 1740128227484
- conda: https://conda.anaconda.org/conda-forge/linux-64/libidn2-2.3.8-ha4ef2c3_0.conda
  sha256: b009d936a67b0cc595cc7b11cde103069a9f334bf39553989705aeaedf2ac6f3
  md5: e155d7130e134619e41dc21276ed6ab5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf >=0.23.1,<1.0a0
  - libgcc >=13
  - libgettextpo >=0.23.1,<1.0a0
  - libunistring >=0,<1.0a0
  license: LGPL-2.0-only
  license_family: LGPL
  size: 137731
  timestamp: 1741525622652
- conda: https://conda.anaconda.org/conda-forge/osx-64/libidn2-2.3.8-he8ff88c_0.conda
  sha256: a83ab8a264ac176dcb946eb0e5d3ef91d09a2b0e065b14e268536baf1f371dab
  md5: a91a277ca9b18f9d788c9d63e6aee379
  depends:
  - __osx >=10.13
  - libasprintf >=0.23.1,<1.0a0
  - libgettextpo >=0.23.1,<1.0a0
  - libintl >=0.23.1,<1.0a0
  - libunistring >=0,<1.0a0
  license: LGPL-2.0-only
  license_family: LGPL
  size: 145176
  timestamp: 1741525744978
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libidn2-2.3.8-h38aa460_0.conda
  sha256: 6ca9944adcbf8f0dba21f631bcd43c4fcf9ebb240258880dff486465cd34c7fe
  md5: 0ec9790e180a73524a591f642579a4f0
  depends:
  - __osx >=11.0
  - libasprintf >=0.23.1,<1.0a0
  - libgettextpo >=0.23.1,<1.0a0
  - libintl >=0.23.1,<1.0a0
  - libunistring >=0,<1.0a0
  license: LGPL-2.0-only
  license_family: LGPL
  size: 146371
  timestamp: 1741525806666
- conda: https://conda.anaconda.org/conda-forge/osx-64/libintl-0.25.1-h3184127_1.conda
  sha256: 8c352744517bc62d24539d1ecc813b9fdc8a785c780197c5f0b84ec5b0dfe122
  md5: a8e54eefc65645193c46e8b180f62d22
  depends:
  - __osx >=10.13
  - libiconv >=1.18,<2.0a0
  license: LGPL-2.1-or-later
  size: 96909
  timestamp: 1753343977382
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libintl-0.25.1-h493aca8_0.conda
  sha256: 99d2cebcd8f84961b86784451b010f5f0a795ed1c08f1e7c76fbb3c22abf021a
  md5: 5103f6a6b210a3912faf8d7db516918c
  depends:
  - __osx >=11.0
  - libiconv >=1.18,<2.0a0
  license: LGPL-2.1-or-later
  size: 90957
  timestamp: 1751558394144
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda
  sha256: 9c0009389c1439ec96a08e3bf7731ac6f0eab794e0a133096556a9ae10be9c27
  md5: 87537967e6de2f885a9fcebd42b7cb10
  depends:
  - __osx >=10.13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 586456
  timestamp: 1745268522731
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libjpeg-turbo-3.1.0-h5505292_0.conda
  sha256: 78df2574fa6aa5b6f5fc367c03192f8ddf8e27dc23641468d54e031ff560b9d4
  md5: 01caa4fbcaf0e6b08b3aef1151e91745
  depends:
  - __osx >=11.0
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  size: 553624
  timestamp: 1745268405713
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-32_h7ac8fdf_openblas.conda
  build_number: 32
  sha256: 5b55a30ed1b3f8195dad9020fe1c6d0f514829bfaaf0cf5e393e93682af009f2
  md5: 6c3f04ccb6c578138e9f9899da0bd714
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  constrains:
  - libcblas   3.9.0   32*_openblas
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17316
  timestamp: 1750388820745
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-32_h236ab99_openblas.conda
  build_number: 32
  sha256: 1e26450b80525b3f656e9c75fd26a10ebaa1d339fe4ca9c7affbebd9acbeac03
  md5: ccdca0c0730ad795e064d81dbe540723
  depends:
  - libblas 3.9.0 32_h7f60823_openblas
  constrains:
  - blas 2.132   openblas
  - liblapacke 3.9.0   32*_openblas
  - libcblas   3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17553
  timestamp: 1750389051033
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapack-3.9.0-32_hc9a63f6_openblas.conda
  build_number: 32
  sha256: 5e1cfa3581d1dec6b07a75084ff6cfa4b4465c646c6884a71c78a28543f83b61
  md5: bf9ead3fa92fd75ad473c6a1d255ffcb
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  constrains:
  - blas 2.132   openblas
  - libcblas   3.9.0   32*_openblas
  - liblapacke 3.9.0   32*_openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17507
  timestamp: 1750388977861
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapacke-3.9.0-32_he2f377e_openblas.conda
  build_number: 32
  sha256: 48e1da503af1b8cfc48c1403c1ea09a5570ce194077adad3d46f15ea95ef4253
  md5: 54e7f7896d0dbf56665bcb0078bfa9d2
  depends:
  - libblas 3.9.0 32_h59b9bed_openblas
  - libcblas 3.9.0 32_he106b2a_openblas
  - liblapack 3.9.0 32_h7ac8fdf_openblas
  constrains:
  - blas 2.132   openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17316
  timestamp: 1750388832284
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblapacke-3.9.0-32_h85686d2_openblas.conda
  build_number: 32
  sha256: 1205a3869775c56bdc5597dd9fb82cc9db697e36800390305395b9b1331c9b46
  md5: 784bde60f3164bc10fbcc86127232c0f
  depends:
  - libblas 3.9.0 32_h7f60823_openblas
  - libcblas 3.9.0 32_hff6cab4_openblas
  - liblapack 3.9.0 32_h236ab99_openblas
  constrains:
  - blas 2.132   openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17577
  timestamp: 1750389063059
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblapacke-3.9.0-32_hbb7bcf8_openblas.conda
  build_number: 32
  sha256: 72579b41e83c546f775543364b7a69dcd9922af6aa38b3f0ab06b9deab2db55c
  md5: 2cf62381fc88b745e4f942677db6bc74
  depends:
  - libblas 3.9.0 32_h10e41b3_openblas
  - libcblas 3.9.0 32_hb3479ef_openblas
  - liblapack 3.9.0 32_hc9a63f6_openblas
  constrains:
  - blas 2.132   openblas
  license: BSD-3-Clause
  license_family: BSD
  size: 17549
  timestamp: 1750388985274
- conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.8-hecd9e04_0.conda
  sha256: a6fddc510de09075f2b77735c64c7b9334cf5a26900da351779b275d9f9e55e1
  md5: 59a7b967b6ef5d63029b1712f8dcf661
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  size: 43987020
  timestamp: 1752141980723
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/osx-64/liblzma-5.8.1-hd471939_2.conda
  sha256: 7e22fd1bdb8bf4c2be93de2d4e718db5c548aa082af47a7430eb23192de6bb36
  md5: 8468beea04b9065b9807fc8b9cdc5894
  depends:
  - __osx >=10.13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 104826
  timestamp: 1749230155443
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/liblzma-5.8.1-h39f12f2_2.conda
  sha256: 0cb92a9e026e7bd4842f410a5c5c665c89b2eb97794ffddba519a626b8ce7285
  md5: d6df911d4564d77c4374b02552cb17d1
  depends:
  - __osx >=11.0
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  size: 92286
  timestamp: 1749230283517
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/osx-64/libnghttp2-1.64.0-hc7306c3_0.conda
  sha256: 0dcfdcf3a445d2d7de4f3b186ab0a794dc872f4ea21622f9b997be72712c027f
  md5: ab21007194b97beade22ceb7a3f6fee5
  depends:
  - __osx >=10.13
  - c-ares >=1.34.2,<2.0a0
  - libcxx >=17
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 606663
  timestamp: 1729572019083
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libnghttp2-1.64.0-h6d7220d_0.conda
  sha256: 00cc685824f39f51be5233b54e19f45abd60de5d8847f1a56906f8936648b72f
  md5: 3408c02539cee5f1141f9f11450b6a51
  depends:
  - __osx >=11.0
  - c-ares >=1.34.2,<2.0a0
  - libcxx >=17
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  size: 566719
  timestamp: 1729572385640
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hb9d3cd8_1.conda
  sha256: 927fe72b054277cde6cb82597d0fcf6baf127dcbce2e0a9d8925a68f1265eef5
  md5: d864d34357c3b65a4b731f78c0801dc4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  license_family: GPL
  size: 33731
  timestamp: 1750274110928
- conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
  sha256: 3b3f19ced060013c2dd99d9d46403be6d319d4601814c772a3472fe2955612b0
  md5: 7c7927b404672409d9917d49bff5f2d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  size: 33418
  timestamp: 1734670021371
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenblas-0.3.30-pthreads_h94d23a6_1.conda
  sha256: 3f3fc30fe340bc7f8f46fea6a896da52663b4d95caed1f144e8ea114b4bb6b61
  md5: 7e2ba4ca7e6ffebb7f7fc2da2744df61
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libgfortran
  - libgfortran5 >=14.3.0
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  size: 5918161
  timestamp: 1753405234435
- conda: https://conda.anaconda.org/conda-forge/osx-64/libopenblas-0.3.30-openmp_hbf64a52_0.conda
  sha256: 933eb95a778657649a66b0e3cf638d591283159954c5e92b3918d67347ed47a1
  md5: 29c54869a3c7d33b6a0add39c5a325fe
  depends:
  - __osx >=10.13
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - llvm-openmp >=18.1.8
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 6179547
  timestamp: 1750380498501
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libopenblas-0.3.30-openmp_hf332438_0.conda
  sha256: 501c8c64f1a6e6b671e49835e6c483bc25f0e7147f3eb4bbb19a4c3673dcaf28
  md5: 5d7dbaa423b4c253c476c24784286e4b
  depends:
  - __osx >=11.0
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - llvm-openmp >=18.1.8
  constrains:
  - openblas >=0.3.30,<0.3.31.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 4163399
  timestamp: 1750378829050
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
  sha256: 215086c108d80349e96051ad14131b751d17af3ed2cb5a34edd62fa89bfe8ead
  md5: 7df50d44d4a14d6c31a2c54f2cd92157
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  size: 50757
  timestamp: 1731330993524
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.50-h943b412_0.conda
  sha256: c7b212bdd3f9d5450c4bae565ccb9385222bf9bb92458c2a23be36ff1b981389
  md5: 51de14db340a848869e69c632b43cca7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 289215
  timestamp: 1751559366724
- conda: https://conda.anaconda.org/conda-forge/osx-64/libpng-1.6.50-h3c4a55f_0.conda
  sha256: a6b51f7056d3f5cf7e71f87314e7b3bb3b6ac5e38a4fb366cf500790e325ffd2
  md5: 0b750895b4a3cbd06e685f86c24c205d
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 267202
  timestamp: 1751559565046
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libpng-1.6.50-h3783ad8_0.conda
  sha256: 38d89e4ceae81f24a11129d2f5e8d10acfc12f057b7b4fd5af9043604a689941
  md5: f39e4bd5424259d8dfcbdbf0e068558e
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  size: 260895
  timestamp: 1751559636317
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.5-h27ae623_0.conda
  sha256: 2dbcef0db82e0e7b6895b6c0dadd3d36c607044c40290c7ca10656f3fca3166f
  md5: 6458be24f09e1b034902ab44fe9de908
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - openldap >=2.6.9,<2.7.0a0
  - openssl >=3.5.0,<4.0a0
  license: PostgreSQL
  size: 2680582
  timestamp: 1746743259857
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/osx-64/libsodium-1.0.20-hfdf4475_0.conda
  sha256: d3975cfe60e81072666da8c76b993af018cf2e73fe55acba2b5ba0928efaccf5
  md5: 6af4b059e26492da6013e79cbcb4d069
  depends:
  - __osx >=10.13
  license: ISC
  size: 210249
  timestamp: 1716828641383
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsodium-1.0.20-h99b78c6_0.conda
  sha256: fade8223e1e1004367d7101dd17261003b60aa576df6d7802191f8972f7470b1
  md5: a7ce36e284c5faaf93c220dfc39e3abd
  depends:
  - __osx >=11.0
  license: ISC
  size: 164972
  timestamp: 1716828607917
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.3-hee844dc_1.conda
  sha256: 8c4faf560815a6d6b5edadc019f76d22a45171eaa707a1f1d1898ceda74b2e3f
  md5: 18d2ac95b507ada9ca159a6bd73255f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=14
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  size: 936339
  timestamp: 1753262589168
- conda: https://conda.anaconda.org/conda-forge/osx-64/libsqlite-3.50.3-h875aaf5_1.conda
  sha256: 3a585d1ddf823a3d7b033196d4aa769971922a984b0735ba741f3cc756a2e576
  md5: 10de0664b3e6f560c7707890aca8174c
  depends:
  - __osx >=10.13
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  size: 984580
  timestamp: 1753262751819
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libsqlite-3.50.3-h4237e3c_1.conda
  sha256: 248ba9622ee91c3ae1266f7b69143adf5031e1f2d94b6d02423e192e47531697
  md5: 6d034f4604ac104a1256204af7d1a534
  depends:
  - __osx >=11.0
  - icu >=75.1,<76.0a0
  - libzlib >=1.3.1,<2.0a0
  license: blessing
  size: 902818
  timestamp: 1753262833682
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
  sha256: fa39bfd69228a13e553bd24601332b7cfeb30ca11a3ca50bb028108fe90a7661
  md5: eecce068c7e4eddeb169591baac20ac4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 304790
  timestamp: 1745608545575
- conda: https://conda.anaconda.org/conda-forge/osx-64/libssh2-1.11.1-hed3591d_0.conda
  sha256: 00654ba9e5f73aa1f75c1f69db34a19029e970a4aeb0fa8615934d8e9c369c3c
  md5: a6cb15db1c2dc4d3a5f6cf3772e09e81
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 284216
  timestamp: 1745608575796
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libssh2-1.11.1-h1590b86_0.conda
  sha256: 8bfe837221390ffc6f111ecca24fa12d4a6325da0c8d131333d63d6c37f27e0a
  md5: b68e8f66b94b44aaa8de4583d3d4cc40
  depends:
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 279193
  timestamp: 1745608793272
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/osx-64/libtiff-4.7.0-h1167cee_5.conda
  sha256: 517a34be9fc697aaf930218f6727a2eff7c38ee57b3b41fd7d1cc0d72aaac562
  md5: fc84af14a09e779f1d37ab1d16d5c4e2
  depends:
  - __osx >=10.13
  - lerc >=4.0.0,<5.0a0
  - libcxx >=18
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 400062
  timestamp: 1747067122967
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libtiff-4.7.0-h2f21f7c_5.conda
  sha256: cc5ee1cffb8a8afb25a4bfd08fce97c5447f97aa7064a055cb4a617df45bc848
  md5: 4eb183bbf7f734f69875702fdbe17ea0
  depends:
  - __osx >=11.0
  - lerc >=4.0.0,<5.0a0
  - libcxx >=18
  - libdeflate >=1.24,<1.25.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  size: 370943
  timestamp: 1747067160710
- conda: https://conda.anaconda.org/conda-forge/linux-64/libunistring-0.9.10-h7f98852_0.tar.bz2
  sha256: e88c45505921db29c08df3439ddb7f771bbff35f95e7d3103bf365d5d6ce2a6d
  md5: 7245a044b4a1980ed83196176b78b73a
  depends:
  - libgcc-ng >=9.3.0
  license: GPL-3.0-only OR LGPL-3.0-only
  size: 1433436
  timestamp: 1626955018689
- conda: https://conda.anaconda.org/conda-forge/osx-64/libunistring-0.9.10-h0d85af4_0.tar.bz2
  sha256: c5805a58cd2b211bffdc8b7cdeba9af3cee456196ab52ab9a30e0353bc95beb7
  md5: 40f27dc16f73256d7b93e53c4f03d92f
  license: GPL-3.0-only OR LGPL-3.0-only
  size: 1392865
  timestamp: 1626955817826
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libunistring-0.9.10-h3422bc3_0.tar.bz2
  sha256: a1afe12ab199f82f339eae83405d293d197f2485d45346a709703bc7e8299949
  md5: d88e77a4861e20bd96bde6628ee7a5ae
  license: GPL-3.0-only OR LGPL-3.0-only
  size: 1577561
  timestamp: 1626955172521
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.6.0-hd42ef1d_0.conda
  sha256: 3aed21ab28eddffdaf7f804f49be7a7d701e8f0e46c856d801270b470820a37b
  md5: aea31d2e5b1091feca96fcfe945c3cf9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  size: 429011
  timestamp: 1752159441324
- conda: https://conda.anaconda.org/conda-forge/osx-64/libwebp-base-1.6.0-hb807250_0.conda
  sha256: 00dbfe574b5d9b9b2b519acb07545380a6bc98d1f76a02695be4995d4ec91391
  md5: 7bb6608cf1f83578587297a158a6630b
  depends:
  - __osx >=10.13
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  size: 365086
  timestamp: 1752159528504
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libwebp-base-1.6.0-h07db88b_0.conda
  sha256: a4de3f371bb7ada325e1f27a4ef7bcc81b2b6a330e46fac9c2f78ac0755ea3dd
  md5: e5e7d467f80da752be17796b87fe6385
  depends:
  - __osx >=11.0
  constrains:
  - libwebp 1.6.0
  license: BSD-3-Clause
  license_family: BSD
  size: 294974
  timestamp: 1752159906788
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/osx-64/libxcb-1.17.0-hf1f96e2_0.conda
  sha256: 8896cd5deff6f57d102734f3e672bc17120613647288f9122bec69098e839af7
  md5: bbeca862892e2898bdb45792a61c4afc
  depends:
  - __osx >=10.13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 323770
  timestamp: 1727278927545
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxcb-1.17.0-hdb1d25a_0.conda
  sha256: bd3816218924b1e43b275863e21a3e13a5db4a6da74cca8e60bc3c213eb62f71
  md5: af523aae2eca6dfa1c8eec693f5b9a79
  depends:
  - __osx >=11.0
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  size: 323658
  timestamp: 1727278733917
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/osx-64/libxml2-2.13.8-h93c44a6_0.conda
  sha256: 4b29663164d7beb9a9066ddcb8578fc67fe0e9b40f7553ea6255cd6619d24205
  md5: e42a93a31cbc6826620144343d42f472
  depends:
  - __osx >=10.13
  - icu >=75.1,<76.0a0
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 609197
  timestamp: 1746634704204
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libxml2-2.13.8-h52572c6_0.conda
  sha256: 13eb825eddce93761d965da3edaf3a42d868c61ece7d9cf21f7e2a13087c2abe
  md5: d7884c7af8af5a729353374c189aede8
  depends:
  - __osx >=11.0
  - icu >=75.1,<76.0a0
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 583068
  timestamp: 1746634531197
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.43-h7a3aeb2_0.conda
  sha256: 35ddfc0335a18677dd70995fa99b8f594da3beb05c11289c87b6de5b930b47a3
  md5: 31059dc620fa57d787e3899ed0421e6d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxml2 >=2.13.8,<2.14.0a0
  license: MIT
  license_family: MIT
  size: 244399
  timestamp: 1753273455036
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/osx-64/libzlib-1.3.1-hd23fc13_2.conda
  sha256: 8412f96504fc5993a63edf1e211d042a1fd5b1d51dedec755d2058948fcced09
  md5: 003a54a4e32b02f7355b50a837e699da
  depends:
  - __osx >=10.13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 57133
  timestamp: 1727963183990
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/libzlib-1.3.1-h8359307_2.conda
  sha256: ce34669eadaba351cd54910743e6a2261b67009624dbc7daeeafdef93616711b
  md5: 369964e85dc26bfe78f41399b366c435
  depends:
  - __osx >=11.0
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  size: 46438
  timestamp: 1727963202283
- conda: https://conda.anaconda.org/conda-forge/osx-64/llvm-openmp-20.1.8-hf4e0ed4_0.conda
  sha256: 9f4161cbb2d17c9622380ec0c59938bd1600324e30a48a770509fbe6d9eee8af
  md5: ab3b31ebe0afdf903fa5ac7f13357e39
  depends:
  - __osx >=10.13
  constrains:
  - openmp 20.1.8|20.1.8.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 308578
  timestamp: 1752565939065
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/llvm-openmp-20.1.8-hbb9b287_0.conda
  sha256: d731910cd4d084574c6bba0638ac98906c1fd8104a2e844f69813e641cf72305
  md5: 6f5b4542c2dd772024d9f7e7b0d5e41a
  depends:
  - __osx >=11.0
  constrains:
  - openmp 20.1.8|20.1.8.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  size: 283218
  timestamp: 1752565794800
- conda: https://conda.anaconda.org/conda-forge/linux-64/mafft-7.526-h4bc722e_0.conda
  sha256: e307b4d817c5f1d1173896d8d54b1b11761b0fc7889bf618ff59dd6407b11d36
  md5: cf3b2acf649bc31e576ed43a2fa4aaa3
  depends:
  - __glibc >=2.17
  - __glibc >=2.17,<3.0.a0
  - gawk
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  size: 2610460
  timestamp: 1720680384725
- conda: https://conda.anaconda.org/conda-forge/osx-64/mafft-7.526-hfdf4475_0.conda
  sha256: 8c161223ae8b8d24fbc8cac5f283ed02dda3ed3eb7d2bfed5fd5caafca1bb4d3
  md5: 1a6e6647325aa44a7522798a8d11146b
  depends:
  - __osx >=10.13
  - gawk
  constrains:
  - __osx>=10.12
  license: BSD-3-Clause
  license_family: BSD
  size: 5356823
  timestamp: 1720669103547
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/mafft-7.526-h99b78c6_0.conda
  sha256: 89b847250232bae9af4fa28c6ffffc857b480dc44d9489ad7ccef25bf271c7d1
  md5: 6c99c90304bcb59719360fdf1aef9704
  depends:
  - __osx >=11.0
  - gawk
  license: BSD-3-Clause
  license_family: BSD
  size: 4921680
  timestamp: 1720669288591
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-3.8.2-pyhd8ed1ab_0.conda
  sha256: d495279d947e01300bfbc124859151be4eec3a088c1afe173323fd3aa89423b2
  md5: b0404922d0459f188768d1e613ed8a87
  depends:
  - importlib-metadata >=4.4
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 80353
  timestamp: 1750360406187
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
  sha256: 0fbacdfb31e55964152b24d5567e9a9996e1e7902fb08eb7d91b5fd6ce60803a
  md5: fee3164ac23dfca50cfcc8b85ddefb81
  depends:
  - mdurl >=0.1,<1
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 64430
  timestamp: 1733250550053
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py312h178313f_1.conda
  sha256: 4a6bf68d2a2b669fecc9a4a009abd1cf8e72c2289522ff00d81b5a6e51ae78f5
  md5: eb227c3e0bf58f5bd69c0532b157975b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 24604
  timestamp: 1733219911494
- conda: https://conda.anaconda.org/conda-forge/osx-64/markupsafe-3.0.2-py312h3520af0_1.conda
  sha256: d521e272f7789ca62e7617058a4ea3bd79efa73de1a39732df209ca5299e64e2
  md5: 32d6bc2407685d7e2d8db424f42018c6
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 23888
  timestamp: 1733219886634
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/markupsafe-3.0.2-py312h998013c_1.conda
  sha256: 4aa997b244014d3707eeef54ab0ee497d12c0d0d184018960cce096169758283
  md5: 46e547061080fddf9cf95a0327e8aba6
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  size: 24048
  timestamp: 1733219945697
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.3-py312h7900ff3_0.conda
  sha256: 2255888d215fb1438b968bd7e5fd89580c25eb90f4010aad38dda8aac7b642c8
  md5: 40e02247b1467ce6fff28cad870dc833
  depends:
  - matplotlib-base >=3.10.3,<3.10.4.0a0
  - pyside6 >=6.7.2
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  size: 17376
  timestamp: 1746820703075
- conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-3.10.3-py312hb401068_0.conda
  sha256: a5562a74e72c91ab4c81945c5b4118a7d3c26aa273eb4eddeba63d4eb49efd50
  md5: ae25ce697cde7c568f325aaa768c39c2
  depends:
  - matplotlib-base >=3.10.3,<3.10.4.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  size: 17452
  timestamp: 1746821036701
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-3.10.3-py312h1f38498_0.conda
  sha256: a73322cb98d14d5eedabfb7dccb2fe239938c5d6bdabfa6d09fecfcdfe1367a1
  md5: 3e3be2c20812f5d46d2e9c2993bbe4a6
  depends:
  - matplotlib-base >=3.10.3,<3.10.4.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  size: 17497
  timestamp: 1746820828995
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py312hd3ec401_0.conda
  sha256: 3b5be100ddfcd5697140dbb8d4126e3afd0147d4033defd6c6eeac78fe089bd2
  md5: 2d69618b52d70970c81cc598e4b51118
  depends:
  - __glibc >=2.17,<3.0.a0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.19,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.12,<3.13.0a0
  - python-dateutil >=2.7
  - python_abi 3.12.* *_cp312
  - qhull >=2020.2,<2020.3.0a0
  - tk >=8.6.13,<8.7.0a0
  license: PSF-2.0
  license_family: PSF
  size: 8188885
  timestamp: 1746820680864
- conda: https://conda.anaconda.org/conda-forge/osx-64/matplotlib-base-3.10.3-py312h535dea3_0.conda
  sha256: a5d1324658d173211db6c78ecbf0b3bd32c85477d293e347820adb528b1719a2
  md5: 8583ca3cb002ae887cbc747f8eb5ffdf
  depends:
  - __osx >=10.13
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libcxx >=18
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - numpy >=1.19,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.12,<3.13.0a0
  - python-dateutil >=2.7
  - python_abi 3.12.* *_cp312
  - qhull >=2020.2,<2020.3.0a0
  license: PSF-2.0
  license_family: PSF
  size: 8221825
  timestamp: 1746821002072
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/matplotlib-base-3.10.3-py312hdbc7e53_0.conda
  sha256: 2ede5ebc11eaf773b1db8cf7ba138ab3b26306bcf84cb9aacb5eb745f150b008
  md5: 00c90634afc6285c57ed54c3ff0247df
  depends:
  - __osx >=11.0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libcxx >=18
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - numpy >=1.19,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python-dateutil >=2.7
  - python_abi 3.12.* *_cp312
  - qhull >=2020.2,<2020.3.0a0
  license: PSF-2.0
  license_family: PSF
  size: 8144960
  timestamp: 1746820800312
- conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
  sha256: 69b7dc7131703d3d60da9b0faa6dd8acbf6f6c396224cf6aef3e855b8c0c41c6
  md5: af6ab708897df59bd6e7283ceab1b56b
  depends:
  - python >=3.9
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  size: 14467
  timestamp: 1733417051523
- conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
  sha256: 78c1bbe1723449c52b7a9df1af2ee5f005209f67e40b6e1d3c7619127c43b1c7
  md5: 592132998493b3ff25fd7479396e8351
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14465
  timestamp: 1733255681319
- conda: https://conda.anaconda.org/conda-forge/noarch/mergedeep-1.3.4-pyhd8ed1ab_1.conda
  sha256: e5b555fd638334a253d83df14e3c913ef8ce10100090e17fd6fb8e752d36f95d
  md5: d9a8fc1f01deae61735c88ec242e855c
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11676
  timestamp: 1734157119152
- conda: https://conda.anaconda.org/bioconda/linux-64/minimap2-2.30-h577a1d6_0.tar.bz2
  sha256: a82a861a2c0e5a01bbe73701dcc6af8ba3f1da430a9fa6c94ed53b5c4a7a2b55
  md5: c90788b08f45337f68b717ee8fd10fea
  depends:
  - k8
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 1357974
  timestamp: 1750025868795
- conda: https://conda.anaconda.org/bioconda/osx-64/minimap2-2.30-h7f84b70_0.tar.bz2
  sha256: 6c854e221a8d7656a55d48cfbf8812c71bb3f9dbbda2bea7cce4b871b78fddbb
  md5: d9f37632e58c407cd23cb39277502a6f
  depends:
  - k8
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 571104
  timestamp: 1750026467075
- conda: https://conda.anaconda.org/bioconda/osx-arm64/minimap2-2.30-hba9b596_0.tar.bz2
  sha256: cdb32f97fa222a0113dea48dab8a3b0bc251f270f1140733f9ba59be981c6404
  md5: 227f3a61a8a0df309eff38a5635640a0
  depends:
  - k8
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  size: 493142
  timestamp: 1750027275243
- conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
  sha256: a67484d7dd11e815a81786580f18b6e4aa2392f292f29183631a6eccc8dc37b3
  md5: 7ec6576e328bc128f4982cd646eeba85
  depends:
  - python >=3.9
  - typing_extensions
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 72749
  timestamp: 1742402716323
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-1.6.1-pyhd8ed1ab_1.conda
  sha256: 902d2e251f9a7ffa7d86a3e62be5b2395e28614bd4dbe5f50acf921fd64a8c35
  md5: 14661160be39d78f2b210f2cc2766059
  depends:
  - click >=7.0
  - colorama >=0.4
  - ghp-import >=1.0
  - importlib-metadata >=4.4
  - jinja2 >=2.11.1
  - markdown >=3.3.6
  - markupsafe >=2.0.1
  - mergedeep >=1.3.4
  - mkdocs-get-deps >=0.2.0
  - packaging >=20.5
  - pathspec >=0.11.1
  - python >=3.9
  - pyyaml >=5.1
  - pyyaml-env-tag >=0.1
  - watchdog >=2.0
  constrains:
  - babel >=2.9.0
  license: BSD-2-Clause
  license_family: BSD
  size: 3524754
  timestamp: 1734344673481
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-autorefs-1.4.2-pyhd8ed1ab_0.conda
  sha256: ed8d25452bd4211a719183c73ef970a54f239d8224125937294396c09fad48ea
  md5: d4468440b32d63e082e0d6c335b19a70
  depends:
  - markdown >=3.3
  - markupsafe >=2.0.1
  - mkdocs >=1.1
  - pymdown-extensions
  - python >=3.9
  license: ISC
  size: 34912
  timestamp: 1747758093008
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-get-deps-0.2.0-pyhd8ed1ab_1.conda
  sha256: e0b501b96f7e393757fb2a61d042015966f6c5e9ac825925e43f9a6eafa907b6
  md5: 84382acddb26c27c70f2de8d4c830830
  depends:
  - importlib-metadata >=4.3
  - mergedeep >=1.3.4
  - platformdirs >=2.2.0
  - python >=3.9
  - pyyaml >=5.1
  license: MIT
  license_family: MIT
  size: 14757
  timestamp: 1734353035244
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-9.6.15-pyhd8ed1ab_0.conda
  sha256: 0959b4a959e7b23970889a03c6ebd7daee10a3839f80250e958c61c86fcd53eb
  md5: de72813ba0ea94ad6f5ab27c89cbc271
  depends:
  - babel >=2.10,<3.dev0
  - backrefs >=5.7.post1,<6.dev0
  - colorama >=0.4,<1.dev0
  - jinja2 >=3.0,<4.dev0
  - markdown >=3.2,<4.dev0
  - mkdocs >=1.6,<2.dev0
  - mkdocs-material-extensions >=1.3,<2.dev0
  - paginate >=0.5,<1.dev0
  - pygments >=2.16,<3.dev0
  - pymdown-extensions >=10.2,<11.dev0
  - python >=3.9
  - requests >=2.26,<3.dev0
  license: MIT
  license_family: MIT
  size: 4917784
  timestamp: 1751382857197
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocs-material-extensions-1.3.1-pyhd8ed1ab_1.conda
  sha256: f62955d40926770ab65cc54f7db5fde6c073a3ba36a0787a7a5767017da50aa3
  md5: de8af4000a4872e16fb784c649679c8e
  depends:
  - python >=3.9
  constrains:
  - mkdocs-material >=5.0.0
  license: MIT
  license_family: MIT
  size: 16122
  timestamp: 1734641109286
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-0.30.0-pyhd8ed1ab_0.conda
  sha256: e5785a8e461444267d5b8e4fcc263e34184d004bb95ae425f925f4fd2585d9b0
  md5: 4107cd44ac3a0f2f12d345cbedcd6bed
  depends:
  - click >=7.0
  - importlib-metadata >=4.6
  - jinja2 >=2.11.1
  - markdown >=3.6
  - markupsafe >=1.1
  - mkdocs >=1.6
  - mkdocs-autorefs >=1.4
  - pymdown-extensions >=6.3
  - python >=3.9,<4.0
  - typing-extensions >=4.1
  license: ISC
  size: 35364
  timestamp: 1753363420566
- conda: https://conda.anaconda.org/conda-forge/noarch/mkdocstrings-python-1.16.12-pyhff2d567_0.conda
  sha256: f07f4a42bb13378305f2702905d35099838de83a235880017d1ae3a0fd401772
  md5: 6c3977dafc75737777349db98cd22d5e
  depends:
  - griffe >=1.6.2
  - mkdocs-autorefs >=1.4
  - mkdocstrings >=0.28.3
  - python >=3.9
  - typing_extensions >=4.0
  license: ISC
  size: 58361
  timestamp: 1748965218001
- conda: https://conda.anaconda.org/bioconda/linux-64/mmseqs2-17.b804f-hd6d6fdc_1.tar.bz2
  sha256: f64af29a82a8642138b34cf7b033b6344c0bd24a6bba898cf653edde6e0f727c
  md5: 561fb589d37cff61ec6b887fc2976498
  depends:
  - _openmp_mutex >=4.5
  - aria2
  - bzip2 >=1.0.8,<2.0a0
  - gawk
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  size: 117468424
  timestamp: 1737229717596
- conda: https://conda.anaconda.org/bioconda/osx-64/mmseqs2-17.b804f-h8b377d6_1.tar.bz2
  sha256: 3c9c38810b7ee49b53e4812a50f948fcc5ed4f0092174f0f9d0a44357f446815
  md5: 461713fc16db011d17c9c9554cc8e320
  depends:
  - aria2
  - bzip2 >=1.0.8,<2.0a0
  - gawk
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  - llvm-openmp >=19.1.7
  - zlib
  license: MIT
  size: 4081489
  timestamp: 1737230789139
- conda: https://conda.anaconda.org/bioconda/osx-arm64/mmseqs2-17.b804f-h44b2af9_1.tar.bz2
  sha256: 160a8254180f24609f2bb7dba7eca3230ed8236dbdc1fdd7c4ff854cfd848a6f
  md5: 38f6ad7264a27a8d2ce37f194c749481
  depends:
  - aria2
  - bzip2 >=1.0.8,<2.0a0
  - gawk
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=18.1.8
  - llvm-openmp >=19.1.7
  - zlib
  license: MIT
  size: 3883210
  timestamp: 1737228514314
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/osx-64/mpfr-4.2.1-haed47dc_3.conda
  sha256: dddb6721dff05b8dfb654c532725330231fcb81ff1e27d885ee0cdcc9fccf1c4
  md5: d511e58aaaabfc23136880d9956fa7a6
  depends:
  - __osx >=10.13
  - gmp >=6.3.0,<7.0a0
  license: LGPL-3.0-only
  license_family: LGPL
  size: 373396
  timestamp: 1725746891597
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/mpfr-4.2.1-hb693164_3.conda
  sha256: 4463e4e2aba7668e37a1b8532859191b4477a6f3602a5d6b4d64ad4c4baaeac5
  md5: 4e4ea852d54cc2b869842de5044662fb
  depends:
  - __osx >=11.0
  - gmp >=6.3.0,<7.0a0
  license: LGPL-3.0-only
  license_family: LGPL
  size: 345517
  timestamp: 1725746730583
- conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyhd8ed1ab_1.conda
  sha256: d09c47c2cf456de5c09fa66d2c3c5035aa1fa228a1983a433c47b876aa16ce90
  md5: 37293a85a0f4f77bbd9cf7aaefc62609
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 15851
  timestamp: 1749895533014
- conda: https://conda.anaconda.org/bioconda/linux-64/muscle-5.3-h9948957_2.tar.bz2
  sha256: 9026e2a9346d00a7d24d051556b6d5c1faf3021d15f572195eadf8c3412bfe0d
  md5: d9d8baf2236da24c5a635d8cfa3c3165
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: GPL-3.0-only
  size: 580461
  timestamp: 1752068202490
- conda: https://conda.anaconda.org/bioconda/osx-64/muscle-5.3-h8e8ab34_2.tar.bz2
  sha256: 1dfc6b88c5516e1e0e806c5d9f81079eaa6b0101a5a75725a34dbfc8b5c66a0c
  md5: 04e393f97e87995f566ac190fb391790
  depends:
  - libcxx >=18
  - llvm-openmp >=18.1.8
  license: GPL-3.0-only
  size: 584804
  timestamp: 1752069894020
- conda: https://conda.anaconda.org/bioconda/osx-arm64/muscle-5.3-h28ef24b_2.tar.bz2
  sha256: 7cb78fd547e0af43643cd51a763f2520cbfdbf043104a7732fa8f3f0c688d45a
  md5: 7b2b90749ef9dbc3cc4baa18028d216f
  depends:
  - libcxx >=18
  - llvm-openmp >=18.1.8
  license: GPL-3.0-only
  size: 536155
  timestamp: 1752068071269
- conda: https://conda.anaconda.org/conda-forge/linux-64/mypy-1.17.0-py312h4c3975b_0.conda
  sha256: f7a427cc6e94fc3c5be83ff0b48509a9a41e9ab48065a14101d0d13b2dd7c9bf
  md5: ebf26f1cfb032d55a2f433ea7a3139f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - mypy_extensions >=1.0.0
  - pathspec >=0.9.0
  - psutil >=4.0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - typing_extensions >=4.6.0
  license: MIT
  license_family: MIT
  size: 18931157
  timestamp: 1752534887158
- conda: https://conda.anaconda.org/conda-forge/osx-64/mypy-1.17.0-py312h2f459f6_0.conda
  sha256: 90ddf6c2cfb92809ff12a59a4582bd407fec861392b788ae0ddbf74cb0730b02
  md5: 0e29f409ddc7bf728ecac96cb46a700f
  depends:
  - __osx >=10.13
  - mypy_extensions >=1.0.0
  - pathspec >=0.9.0
  - psutil >=4.0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - typing_extensions >=4.6.0
  license: MIT
  license_family: MIT
  size: 12704250
  timestamp: 1752535007235
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/mypy-1.17.0-py312h163523d_0.conda
  sha256: 0fb78906dbcb3daa1c9b9aa9047985ef1cacf2d2e64d271c1b9e7570ae4ed1fc
  md5: 77b10261bad8e87c9ebb6cebd493c394
  depends:
  - __osx >=11.0
  - mypy_extensions >=1.0.0
  - pathspec >=0.9.0
  - psutil >=4.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - typing_extensions >=4.6.0
  license: MIT
  license_family: MIT
  size: 10410020
  timestamp: 1752534951317
- conda: https://conda.anaconda.org/conda-forge/noarch/mypy_extensions-1.1.0-pyha770c72_0.conda
  sha256: 6ed158e4e5dd8f6a10ad9e525631e35cee8557718f83de7a4e3966b1f772c4b1
  md5: e9c622e0d00fa24a6292279af3ab6d06
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11766
  timestamp: 1745776666688
- conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-2.0.1-pyhe01879c_0.conda
  sha256: 167ed2f6100909830863531faa2dce250eedee78f2d64c4e5506dc3f3ae3c354
  md5: 5f0dea40791cecf0f82882b9eea7f7c1
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 240527
  timestamp: 1753814733349
- conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
  sha256: a20cff739d66c2f89f413e4ba4c6f6b59c50d5c30b5f0d840c13e8c9c2df9135
  md5: 6bb0d77277061742744176ab555b723c
  depends:
  - jupyter_client >=6.1.12
  - jupyter_core >=4.12,!=5.0.*
  - nbformat >=5.1
  - python >=3.8
  - traitlets >=5.4
  license: BSD-3-Clause
  license_family: BSD
  size: 28045
  timestamp: 1734628936013
- conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
  sha256: dcccb07c5a1acb7dc8be94330e62d54754c0e9c9cb2bb6865c8e3cfe44cf5a58
  md5: d24beda1d30748afcc87c429454ece1b
  depends:
  - beautifulsoup4
  - bleach-with-css !=5.0.0
  - defusedxml
  - importlib-metadata >=3.6
  - jinja2 >=3.0
  - jupyter_core >=4.7
  - jupyterlab_pygments
  - markupsafe >=2.0
  - mistune >=2.0.3,<4
  - nbclient >=0.5.0
  - nbformat >=5.7
  - packaging
  - pandocfilters >=1.4.1
  - pygments >=2.4.1
  - python >=3.9
  - traitlets >=5.1
  - python
  constrains:
  - pandoc >=2.9.2,<4.0.0
  - nbconvert ==7.16.6 *_0
  license: BSD-3-Clause
  license_family: BSD
  size: 200601
  timestamp: 1738067871724
- conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
  sha256: 7a5bd30a2e7ddd7b85031a5e2e14f290898098dc85bea5b3a5bf147c25122838
  md5: bbe1963f1e47f594070ffe87cdf612ea
  depends:
  - jsonschema >=2.6
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-fastjsonschema >=2.15
  - traitlets >=5.1
  license: BSD-3-Clause
  license_family: BSD
  size: 100945
  timestamp: 1733402844974
- conda: https://conda.anaconda.org/bioconda/linux-64/ncbi-vdb-3.2.1-h9948957_0.tar.bz2
  sha256: f42b1398b178b6de64a9d180ef29bac9192b5fcd9492eced4eca854c22f6aba9
  md5: c80d2359e66e4dd94bceedd55b752dff
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: Public Domain
  size: 11067140
  timestamp: 1742339687061
- conda: https://conda.anaconda.org/bioconda/osx-64/ncbi-vdb-3.2.1-h5fa12a8_0.tar.bz2
  sha256: abea139f5f455ebdcd4f2f9aeac30c82fcbb503de3a78560d0c1a9baa0a38364
  md5: 9d002e46dd389ee463d90eb2848a5718
  depends:
  - libcxx >=18
  license: Public Domain
  size: 10025688
  timestamp: 1742340468285
- conda: https://conda.anaconda.org/bioconda/osx-arm64/ncbi-vdb-3.2.1-h4675bf2_0.tar.bz2
  sha256: d956b1058fafd393a3f023430745d62c27705870728082e19cafca1e2125fa04
  md5: ea6c0a2178b7d65512f3d02001f7d836
  depends:
  - libcxx >=18
  license: Public Domain
  size: 9594540
  timestamp: 1742339704756
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/osx-64/ncurses-6.5-h0622a9a_3.conda
  sha256: ea4a5d27ded18443749aefa49dc79f6356da8506d508b5296f60b8d51e0c4bd9
  md5: ced34dd9929f491ca6dab6a2927aff25
  depends:
  - __osx >=10.13
  license: X11 AND BSD-3-Clause
  size: 822259
  timestamp: 1738196181298
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ncurses-6.5-h5e97a16_3.conda
  sha256: 2827ada40e8d9ca69a153a45f7fd14f32b2ead7045d3bbb5d10964898fe65733
  md5: 068d497125e4bf8a66bf707254fff5ae
  depends:
  - __osx >=11.0
  license: X11 AND BSD-3-Clause
  size: 797030
  timestamp: 1738196177597
- conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
  sha256: bb7b21d7fd0445ddc0631f64e66d91a179de4ba920b8381f29b9d006a42788c0
  md5: 598fd7d4d0de2455fb74f56063969a97
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 11543
  timestamp: 1733325673691
- conda: https://conda.anaconda.org/conda-forge/noarch/nodeenv-1.9.1-pyhd8ed1ab_1.conda
  sha256: 3636eec0e60466a00069b47ce94b6d88b01419b6577d8e393da44bb5bc8d3468
  md5: 7ba3f09fceae6a120d664217e58fe686
  depends:
  - python >=3.9
  - setuptools
  license: BSD-3-Clause
  license_family: BSD
  size: 34574
  timestamp: 1734112236147
- conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
  sha256: 7b920e46b9f7a2d2aa6434222e5c8d739021dbc5cc75f32d124a8191d86f9056
  md5: e7f89ea5f7ea9401642758ff50a2d9c1
  depends:
  - jupyter_server >=1.8,<3
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 16817
  timestamp: 1733408419340
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-2.3.2-py312h33ff503_0.conda
  sha256: d54e52df67e0be7e5faa9e6f0efccea3d72f635a3159cc151c4668e5159f6ef3
  md5: 3f6efbc40eb13f019c856c410fa921d2
  depends:
  - python
  - libgcc >=14
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=14
  - libgcc >=14
  - libblas >=3.9.0,<4.0a0
  - python_abi 3.12.* *_cp312
  - libcblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  size: 8785045
  timestamp: 1753401550884
- conda: https://conda.anaconda.org/conda-forge/osx-64/numpy-2.3.2-py312hda18a35_0.conda
  sha256: 911e18e2f26946dfe49786186ea27b855dc6e70702b621c395ba67caa583d396
  md5: 0349ca0107bd6613fa305d0b3a08e23a
  depends:
  - python
  - __osx >=10.13
  - libcxx >=19
  - python_abi 3.12.* *_cp312
  - libcblas >=3.9.0,<4.0a0
  - liblapack >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  size: 7944998
  timestamp: 1753401533366
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/numpy-2.3.2-py312h2f38b44_0.conda
  sha256: 581039072c18b2abd8dfcf7fe5c16a8fbb72e14821bad4817ca00dbb16f3bad3
  md5: c58a6fa1ee8edb9de10d0f5c91806193
  depends:
  - python
  - libcxx >=19
  - python 3.12.* *_cpython
  - __osx >=11.0
  - liblapack >=3.9.0,<4.0a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - python_abi 3.12.* *_cp312
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  size: 6657726
  timestamp: 1753401542508
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
  sha256: 5bee706ea5ba453ed7fd9da7da8380dd88b865c8d30b5aaec14d2b6dd32dbc39
  md5: 9e5816bc95d285c115a3ebc2f8563564
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 342988
  timestamp: 1733816638720
- conda: https://conda.anaconda.org/conda-forge/osx-64/openjpeg-2.5.3-h7fd6d84_0.conda
  sha256: faea03f36c9aa3524c911213b116da41695ff64b952d880551edee2843fe115b
  md5: 025c711177fc3309228ca1a32374458d
  depends:
  - __osx >=10.13
  - libcxx >=18
  - libpng >=1.6.44,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 332320
  timestamp: 1733816828284
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openjpeg-2.5.3-h8a3d83b_0.conda
  sha256: 1d59bc72ca7faac06d349c1a280f5cfb8a57ee5896f1e24225a997189d7418c7
  md5: 4b71d78648dbcf68ce8bf22bb07ff838
  depends:
  - __osx >=11.0
  - libcxx >=18
  - libpng >=1.6.44,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  size: 319362
  timestamp: 1733816781741
- conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
  sha256: cb0b07db15e303e6f0a19646807715d28f1264c6350309a559702f4f34f37892
  md5: 2e5bf4f1da39c0b32778561c3c4e5878
  depends:
  - __glibc >=2.17,<3.0.a0
  - cyrus-sasl >=2.1.27,<3.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  license: OLDAP-2.8
  license_family: BSD
  size: 780253
  timestamp: 1748010165522
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.1-h7b32b05_0.conda
  sha256: 942347492164190559e995930adcdf84e2fea05307ec8012c02a505f5be87462
  md5: c87df2ab1448ba69169652ab9547082d
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  size: 3131002
  timestamp: 1751390382076
- conda: https://conda.anaconda.org/conda-forge/osx-64/openssl-3.5.1-hc426f3f_0.conda
  sha256: d5dc7da2ef7502a14f88443675c4894db336592ac7b9ae0517e1339ebb94f38a
  md5: f1ac2dbc36ce2017bd8f471960b1261d
  depends:
  - __osx >=10.13
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  size: 2744123
  timestamp: 1751391059798
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/openssl-3.5.1-h81ee809_0.conda
  sha256: f94fde0f096fa79794c8aa0a2665630bbf9026cc6438e8253f6555fc7281e5a8
  md5: a8ac77e7c7e58d43fa34d60bd4361062
  depends:
  - __osx >=11.0
  - ca-certificates
  license: Apache-2.0
  license_family: Apache
  size: 3071649
  timestamp: 1751390309393
- conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
  sha256: 1840bd90d25d4930d60f57b4f38d4e0ae3f5b8db2819638709c36098c6ba770c
  md5: e51f1e4089cad105b6cac64bd8166587
  depends:
  - python >=3.9
  - typing_utils
  license: Apache-2.0
  license_family: APACHE
  size: 30139
  timestamp: 1734587755455
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/noarch/paginate-0.5.7-pyhd8ed1ab_1.conda
  sha256: f6fef1b43b0d3d92476e1870c08d7b9c229aebab9a0556b073a5e1641cf453bd
  md5: c3f35453097faf911fd3f6023fc2ab24
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 18865
  timestamp: 1734618649164
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.3.1-py312hf79963d_0.conda
  sha256: 6ec86b1da8432059707114270b9a45d767dac97c4910ba82b1f4fa6f74e077c8
  md5: 7c73e62e62e5864b8418440e2a2cc246
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.12.* *_cp312
  - pytz >=2020.1
  constrains:
  - html5lib >=1.1
  - fastparquet >=2022.12.0
  - xarray >=2022.12.0
  - pyqt5 >=5.15.9
  - pyxlsb >=1.0.10
  - matplotlib >=3.6.3
  - numba >=0.56.4
  - odfpy >=1.4.1
  - bottleneck >=1.3.6
  - tabulate >=0.9.0
  - scipy >=1.10.0
  - pyreadstat >=1.2.0
  - pandas-gbq >=0.19.0
  - openpyxl >=3.1.0
  - xlrd >=2.0.1
  - pyarrow >=10.0.1
  - xlsxwriter >=3.0.5
  - python-calamine >=0.1.7
  - gcsfs >=2022.11.0
  - zstandard >=0.19.0
  - fsspec >=2022.11.0
  - lxml >=4.9.2
  - s3fs >=2022.11.0
  - numexpr >=2.8.4
  - psycopg2 >=2.9.6
  - qtpy >=2.3.0
  - pytables >=3.8.0
  - tzdata >=2022.7
  - sqlalchemy >=2.0.0
  - beautifulsoup4 >=4.11.2
  - blosc >=1.21.3
  license: BSD-3-Clause
  license_family: BSD
  size: 15092371
  timestamp: 1752082221274
- conda: https://conda.anaconda.org/conda-forge/osx-64/pandas-2.3.1-py312hbf2c5ff_0.conda
  sha256: a0c3c20b33e449690d0bcef2f2589d6b8b4ed65498d82bd0935ed735fcf07e3f
  md5: b54f2b1bc50bbe54852f0b790313bfe8
  depends:
  - __osx >=10.13
  - libcxx >=19
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.12.* *_cp312
  - pytz >=2020.1
  constrains:
  - fsspec >=2022.11.0
  - scipy >=1.10.0
  - fastparquet >=2022.12.0
  - zstandard >=0.19.0
  - numba >=0.56.4
  - python-calamine >=0.1.7
  - pyreadstat >=1.2.0
  - psycopg2 >=2.9.6
  - matplotlib >=3.6.3
  - xlrd >=2.0.1
  - bottleneck >=1.3.6
  - html5lib >=1.1
  - s3fs >=2022.11.0
  - pyarrow >=10.0.1
  - odfpy >=1.4.1
  - beautifulsoup4 >=4.11.2
  - pyxlsb >=1.0.10
  - xarray >=2022.12.0
  - sqlalchemy >=2.0.0
  - pytables >=3.8.0
  - pyqt5 >=5.15.9
  - tabulate >=0.9.0
  - qtpy >=2.3.0
  - blosc >=1.21.3
  - openpyxl >=3.1.0
  - tzdata >=2022.7
  - gcsfs >=2022.11.0
  - xlsxwriter >=3.0.5
  - numexpr >=2.8.4
  - lxml >=4.9.2
  - pandas-gbq >=0.19.0
  license: BSD-3-Clause
  license_family: BSD
  size: 14253723
  timestamp: 1752082246640
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pandas-2.3.1-py312h98f7732_0.conda
  sha256: f4f98436dde01309935102de2ded045bb5500b42fb30a3bf8751b15affee4242
  md5: d3775e9b27579a0e96150ce28a2542bd
  depends:
  - __osx >=11.0
  - libcxx >=19
  - numpy >=1.22.4
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.12.* *_cp312
  - pytz >=2020.1
  constrains:
  - openpyxl >=3.1.0
  - pyarrow >=10.0.1
  - s3fs >=2022.11.0
  - zstandard >=0.19.0
  - psycopg2 >=2.9.6
  - fastparquet >=2022.12.0
  - fsspec >=2022.11.0
  - qtpy >=2.3.0
  - blosc >=1.21.3
  - xlsxwriter >=3.0.5
  - xarray >=2022.12.0
  - python-calamine >=0.1.7
  - tabulate >=0.9.0
  - odfpy >=1.4.1
  - numexpr >=2.8.4
  - tzdata >=2022.7
  - scipy >=1.10.0
  - pyreadstat >=1.2.0
  - beautifulsoup4 >=4.11.2
  - numba >=0.56.4
  - pyqt5 >=5.15.9
  - pytables >=3.8.0
  - lxml >=4.9.2
  - xlrd >=2.0.1
  - matplotlib >=3.6.3
  - bottleneck >=1.3.6
  - pandas-gbq >=0.19.0
  - html5lib >=1.1
  - pyxlsb >=1.0.10
  - sqlalchemy >=2.0.0
  - gcsfs >=2022.11.0
  license: BSD-3-Clause
  license_family: BSD
  size: 13991815
  timestamp: 1752082557265
- conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
  sha256: 2bb9ba9857f4774b85900c2562f7e711d08dd48e2add9bee4e1612fbee27e16f
  md5: 457c2c8c08e54905d6954e79cb5b5db9
  depends:
  - python !=3.0,!=3.1,!=3.2,!=3.3
  license: BSD-3-Clause
  license_family: BSD
  size: 11627
  timestamp: 1631603397334
- conda: https://conda.anaconda.org/bioconda/linux-64/parasail-python-1.3.4-py312hdcc493e_5.tar.bz2
  sha256: 202f912197ef21e438a489fbc699cb16f8d9976ecb60864959f9b40ed0325feb
  md5: 15ab3e8cd7b0cc3edc63d3e37b5acf6e
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - numpy
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 3457508
  timestamp: 1753165696653
- conda: https://conda.anaconda.org/bioconda/osx-64/parasail-python-1.3.4-py312h13dbd8f_5.tar.bz2
  sha256: 6122d14fa08f9afb67ce2e8305ee560e031f5081cc7807d57441cdb08ac38eb6
  md5: 3493a981eacb87baaaa2574250156d81
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - numpy
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 2572627
  timestamp: 1753168318688
- conda: https://conda.anaconda.org/bioconda/osx-arm64/parasail-python-1.3.4-py312hd60a339_5.tar.bz2
  sha256: d03eb5155d0498c9d50cc01d1c6f8d98a3b5e475601adee1b5b330c3b28b1043
  md5: 6d01c7c9d4aa13940e630c84eea61709
  depends:
  - libcxx >=18
  - libzlib >=1.3.1,<2.0a0
  - numpy
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 1227250
  timestamp: 1753164814454
- conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
  sha256: 17131120c10401a99205fc6fe436e7903c0fa092f1b3e80452927ab377239bcc
  md5: 5c092057b6badd30f75b06244ecd01c9
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 75295
  timestamp: 1733271352153
- conda: https://conda.anaconda.org/conda-forge/noarch/pathspec-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9f64009cdf5b8e529995f18e03665b03f5d07c0b17445b8badef45bde76249ee
  md5: 617f15191456cc6a13db418a275435e5
  depends:
  - python >=3.9
  license: MPL-2.0
  license_family: MOZILLA
  size: 41075
  timestamp: 1733233471940
- conda: https://conda.anaconda.org/conda-forge/noarch/patsy-1.0.1-pyhd8ed1ab_1.conda
  sha256: ab52916f056b435757d46d4ce0a93fd73af47df9c11fd72b74cc4b7e1caca563
  md5: ee23fabfd0a8c6b8d6f3729b47b2859d
  depends:
  - numpy >=1.4.0
  - python >=3.9
  license: BSD-2-Clause AND PSF-2.0
  license_family: BSD
  size: 186594
  timestamp: 1733792482894
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-5.32.1-7_hd590300_perl5.conda
  build_number: 7
  sha256: 9ec32b6936b0e37bcb0ed34f22ec3116e75b3c0964f9f50ecea5f58734ed6ce9
  md5: f2cfec9406850991f4e3d960cc9e3321
  depends:
  - libgcc-ng >=12
  - libxcrypt >=4.4.36
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 13344463
  timestamp: 1703310653947
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-5.32.1-7_h10d778d_perl5.conda
  build_number: 7
  sha256: 8ebd35e2940055a93135b9fd11bef3662cecef72d6ee651f68d64a2f349863c7
  md5: dc442e0885c3a6b65e61c61558161a9e
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 12334471
  timestamp: 1703311001432
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-5.32.1-7_h4614cfb_perl5.conda
  build_number: 7
  sha256: b0c55040d2994fd6bf2f83786561d92f72306d982d6ea12889acad24a9bf43b8
  md5: ba3cbe93f99e896765422cc5f7c3a79e
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 14439531
  timestamp: 1703311335652
- conda: https://conda.anaconda.org/bioconda/noarch/perl-archive-tar-3.04-pl5321hdfd78af_0.tar.bz2
  sha256: ff80ba34551d4e051f4eaff48df53a6c63695dcf00e670c89a241a04f0b662ae
  md5: 27d0df347c48c48b363651b64fb6fb4c
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  - perl-io-compress
  - perl-io-zlib
  - perl-pathtools
  license: Perl_5
  size: 35243
  timestamp: 1749770047223
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-carp-1.50-pl5321hd8ed1ab_0.tar.bz2
  sha256: 1981e31113e1e77a2cdc13db657c636f047cd3be2a64d9a0bffac03c5427c1bd
  md5: bdddc03e28019b902da71b722f2288d7
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  - perl-exporter
  - perl-extutils-makemaker
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 22257
  timestamp: 1636653208008
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-common-sense-3.75-pl5321hd8ed1ab_0.tar.bz2
  sha256: 38ef218e9b9d55b9fbdce6b31cf81bcf6f1b16f21b8e7cb9279b41399522a320
  md5: ef70dc77e8b10bbb62f5e843b401ef0e
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 20291
  timestamp: 1660429950685
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-bzip2-2.201-pl5321hbf60520_1.conda
  sha256: c90cb25019a79f148d2a3d443e0b8e65b18460debc2ef304447c42191be7a042
  md5: 25d372c472b138d798469104d6864af9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 55096
  timestamp: 1741381504472
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-bzip2-2.201-pl5321haeee4d3_1.conda
  sha256: f6e36f018f3e06510f32da7f2865a5204016f4d74484859563ddb18e9c330871
  md5: b5dbc24c82578b9549a47e8c04b6eebc
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 58147
  timestamp: 1741381557727
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-bzip2-2.201-pl5321h9337747_1.conda
  sha256: b625d0915b87e92a058eff05967176941dfd47eb4b1d7a43af42b503bfca40df
  md5: 1193f4ab7cfc62e6838c97f46e39adc9
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 53766
  timestamp: 1741381601494
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-compress-raw-zlib-2.202-pl5321hadc24fc_0.conda
  sha256: 60b82eceaa11890a406c1f7fc9b6d589ca1bfc99589c19b8a5848895447f5def
  md5: 2eecc7fe28e0342391a74e86d8c2dfec
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 80095
  timestamp: 1730368203088
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-compress-raw-zlib-2.202-pl5321h0aa47d9_0.conda
  sha256: 78feafc1e51e334cbd7d5569be0841a48d1140bbab45aa8cfe2b5ad41d1f1b9e
  md5: 4a65e9e75232e882fbd9787a737c34ab
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 75704
  timestamp: 1730368267075
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-compress-raw-zlib-2.202-pl5321hb76e6fb_0.conda
  sha256: 8e602a8951ce252c69954db61ab3dfe2497dd22d6ae36962f2ecc3b2e4adf742
  md5: 78c333eb7b46422908b84c9783fef792
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 74337
  timestamp: 1731397834839
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-encode-3.21-pl5321hb9d3cd8_1.conda
  sha256: 4cbe4125efe8763e1ad44448852b04481002f1dac84b6052cec1626df79e3a16
  md5: a418a0e7010007df65768e4e4b96dd14
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-exporter
  - perl-parent
  - perl-storable
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 1731511
  timestamp: 1728247059262
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-encode-3.21-pl5321h6e16a3a_1.conda
  sha256: 2076098715d5ca61c043f88048b06c1218e7ce87a196a3132467620a78844a43
  md5: 36bce9916ae172be82e3d65a6fdb2171
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-exporter
  - perl-parent
  - perl-storable
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 1051857
  timestamp: 1741897736721
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-encode-3.21-pl5321h5505292_1.conda
  sha256: 7a781b37b0100f61d07136dce26d3f5730ae47b65918ceacf7b9fe9742c41049
  md5: 42eb64f1b92569f629eb7da894bbfaf0
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-exporter
  - perl-parent
  - perl-storable
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 1086399
  timestamp: 1741897764138
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-5.74-pl5321hd8ed1ab_0.tar.bz2
  sha256: 42271d0b79043a10a89044acb5febea50046b745dd2fc37e02943bc3bc75bf8e
  md5: fd2eac4e35f8c970870a3961c1df3e29
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 19071
  timestamp: 1636696009075
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-exporter-tiny-1.002002-pl5321hd8ed1ab_0.tar.bz2
  sha256: abdf86828a12a389d0feb0d70501b267842557bae11820e266526aeb6ab2bebe
  md5: 48d709826875be1f2c108d3d1d8efec7
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 28592
  timestamp: 1660341479867
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-extutils-makemaker-7.70-pl5321hd8ed1ab_0.conda
  sha256: 1d3f342ca74cf2948c3edcfe0d3367b1db0fc64bb163393a2e025336dec3a40c
  md5: ec3e57ed34f7765bfc7054a05868ce5d
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 157323
  timestamp: 1679847836884
- conda: https://conda.anaconda.org/bioconda/linux-64/perl-io-compress-2.201-pl5321h503566f_5.tar.bz2
  sha256: 11aa8a5e1b14e96588a3ee012b8d5e61459e8f0d07c2aabbfbfd9f89d18057c9
  md5: 3f56091e7e231ab35f4675460b2d969c
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-compress-raw-bzip2 >=2.103
  - perl-compress-raw-zlib >=2.103
  - perl-encode
  - perl-scalar-list-utils
  license: perl_5
  size: 86778
  timestamp: 1741903835057
- conda: https://conda.anaconda.org/bioconda/osx-64/perl-io-compress-2.201-pl5321h5eaf441_5.tar.bz2
  sha256: d37bfa9803ee5db295f39cabcb95a3c6ed5fa079823298a9e2ead52ad0fd6cce
  md5: 81863850aca30fe2be8901ae6ec3d4aa
  depends:
  - libcxx >=18
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-compress-raw-bzip2 >=2.103
  - perl-compress-raw-zlib >=2.103
  - perl-encode
  - perl-scalar-list-utils
  license: perl_5
  size: 86867
  timestamp: 1741904334358
- conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-io-compress-2.201-pl5321haef7865_5.tar.bz2
  sha256: 1272e737a57bde3cb7e6358e446e279855909baee119ef6506814907fc609193
  md5: 128100ab8e9ddf5a357fc05b9c651762
  depends:
  - libcxx >=18
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-compress-raw-bzip2 >=2.103
  - perl-compress-raw-zlib >=2.103
  - perl-encode
  - perl-scalar-list-utils
  license: perl_5
  size: 86921
  timestamp: 1741903736050
- conda: https://conda.anaconda.org/bioconda/noarch/perl-io-zlib-1.15-pl5321hdfd78af_1.tar.bz2
  sha256: 771a44b338cac68a22893897450222b886e24bbb291b014897d561f2ba3b588f
  md5: db92645dabe9467115729e4479841b5d
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 12760
  timestamp: 1752069858135
- conda: https://conda.anaconda.org/bioconda/noarch/perl-json-4.10-pl5321hdfd78af_1.tar.bz2
  sha256: c30768595793865d67fe2bf76a34e696a0664ae1f1cef34e31cb16618af22d61
  md5: c6c43c11e14d90b836f42c611e106ea9
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  - perl-json-xs
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 57728
  timestamp: 1722414214816
- conda: https://conda.anaconda.org/bioconda/linux-64/perl-json-xs-4.03-pl5321h9948957_4.tar.bz2
  sha256: b651493701207198510246cacadede6698267228b6745f31cea48d1c29cacca5
  md5: dac4cd5a18498c564fd4e969fb530bd1
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-common-sense
  - perl-types-serialiser
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 74836
  timestamp: 1734303359179
- conda: https://conda.anaconda.org/bioconda/osx-64/perl-json-xs-4.03-pl5321h5fa12a8_4.tar.bz2
  sha256: 45e64af528342d93b95c4713bb35ea57a1b4ffe341dfd46518c44fbe13ffefb1
  md5: dcfbe2a2e3af52ec2280186e24c75d3d
  depends:
  - libcxx >=18
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-common-sense
  - perl-types-serialiser
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 73997
  timestamp: 1733994678480
- conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-json-xs-4.03-pl5321h4675bf2_4.tar.bz2
  sha256: 7ef80f82e108360c2913af70bc76650fa67727d700ebac8e2e30911a12f0cc07
  md5: e9b968369a80482967f5a3868ab47ae8
  depends:
  - libcxx >=18
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-common-sense
  - perl-types-serialiser
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  size: 75042
  timestamp: 1733982738504
- conda: https://conda.anaconda.org/bioconda/noarch/perl-list-moreutils-0.430-pl5321hdfd78af_0.tar.bz2
  sha256: 2190cc8430bb218ea80f5fc5e2bf75e4e20a27fb83e8c3cb789a18c32854a56c
  md5: 7f04c79d216d0f8e7b6d5a51de4aafa0
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  - perl-exporter-tiny
  - perl-list-moreutils-xs >=0.430
  license: apache_2_0
  size: 32468
  timestamp: 1644871004171
- conda: https://conda.anaconda.org/bioconda/linux-64/perl-list-moreutils-xs-0.430-pl5321h7b50bb2_5.tar.bz2
  sha256: fe2d360770fe5b856ee1e625eac6b07cea386c64b0866e323c6535eb62b9ceee
  md5: 9192770eb08524038c3fcf24e915b10c
  depends:
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: apache_2_0
  size: 51506
  timestamp: 1741776389435
- conda: https://conda.anaconda.org/bioconda/osx-64/perl-list-moreutils-xs-0.430-pl5321h18d8cf3_5.tar.bz2
  sha256: eea554693fbc015804eec6c510cca54d0051bcd1d496bfbfac6fe4426f42503f
  md5: 2a311e115963e9a985b1029d092a58d1
  depends:
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: apache_2_0
  size: 44178
  timestamp: 1741776966235
- conda: https://conda.anaconda.org/bioconda/osx-arm64/perl-list-moreutils-xs-0.430-pl5321hbdacb55_5.tar.bz2
  sha256: 46d9645a60edde726f5891ae254db4bb2a17f0e32ff64a18160910c4a618d879
  md5: 4f44dd7b4232e9c202a72d17f9d1c287
  depends:
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: apache_2_0
  size: 43423
  timestamp: 1741776343291
- conda: https://conda.anaconda.org/conda-forge/noarch/perl-parent-0.243-pl5321hd8ed1ab_0.conda
  sha256: ec57d9e56ba86d840d5a9e65c665365fb6a290851cd13e6719628f3295eabf34
  md5: 314caa3b72d65f8078d426c6721dcacc
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 13933
  timestamp: 1733429736293
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-pathtools-3.75-pl5321hb9d3cd8_2.conda
  sha256: a80bc265aa749ae03fdd6d5f2098312ea6f43cc193ea7aba0e6e4304d84ce8c0
  md5: 03d88c89dfac8a26adcdeff242f94007
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-carp
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 50681
  timestamp: 1741783312134
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-pathtools-3.75-pl5321h6e16a3a_2.conda
  sha256: f04c191928f2ca80470cceb51fe40c3482f691424a54bd6b8081256dc0dcdf99
  md5: 8b5ca77bb3f633c6b837c31c71d3a255
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-carp
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 48818
  timestamp: 1741783288987
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-pathtools-3.75-pl5321hc71e825_2.conda
  sha256: 85eca77264dfe63673098dbc5d07a576c20f8aca6fada79041ad4ca24742d83d
  md5: c27e14dadd37c355bd3160a0a4830653
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  - perl-carp
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 49073
  timestamp: 1741783420655
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-scalar-list-utils-1.69-pl5321hb9d3cd8_0.conda
  sha256: 4172b82a2988352bac5ecf6c10d2868a75d92fc711a8a8f483c40576dc1f01f1
  md5: 3efe8ae5f0579eda2b5bba2ec8af36fd
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 51722
  timestamp: 1743570853926
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-scalar-list-utils-1.69-pl5321h6e16a3a_0.conda
  sha256: 5f90e1d85d9320523504caa4f4c8c07206b3abb2ad5c098ccf12e78e52e3656d
  md5: 8cad52b46f6cf2b7584b8fa2c30fc8c7
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 47695
  timestamp: 1743570856666
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-scalar-list-utils-1.69-pl5321hc71e825_0.conda
  sha256: c0e6f2091272cc5bef0918eb416d5116f551902b8ccdab84f178ff1a0566f36f
  md5: ba2a608a812ba88aa858f6412d757030
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 47075
  timestamp: 1743570872677
- conda: https://conda.anaconda.org/conda-forge/linux-64/perl-storable-3.15-pl5321hb9d3cd8_2.conda
  sha256: 25beba40154a394189d0ff2afd31683d79c9106d47e77e12d20900d053dffaf6
  md5: 212f63a5c7c753ecf0a74c349b9270c5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 71475
  timestamp: 1741353849888
- conda: https://conda.anaconda.org/conda-forge/osx-64/perl-storable-3.15-pl5321h6e16a3a_2.conda
  sha256: 99f7c02df7d6a3d1649903dad9088b24a0c1fc71f3ed42dd8603e7b5c7cb1413
  md5: 6210779889244bfdf2a339a6f00cdb7c
  depends:
  - __osx >=10.13
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 65428
  timestamp: 1741353959926
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/perl-storable-3.15-pl5321hc71e825_2.conda
  sha256: 7e0cfec98f6a4ee7f4ce7e18d9efc748a8bdf1462081a1897c445f709a791d0c
  md5: 8574488332db7649d4f87120553ec179
  depends:
  - __osx >=11.0
  - perl >=5.32.1,<5.33.0a0 *_perl5
  license: GPL-1.0-or-later OR Artistic-1.0-Perl
  license_family: OTHER
  size: 63402
  timestamp: 1741353964208
- conda: https://conda.anaconda.org/bioconda/noarch/perl-types-serialiser-1.01-pl5321hdfd78af_0.tar.bz2
  sha256: 20f61217b16235d0161ad6fa0a234585afcc04ec5a6142c65b5867e26216dfe3
  md5: cfc65753e827bbef80c00eaa395f6ae7
  depends:
  - perl >=5.32.1,<6.0a0 *_perl5
  - perl-common-sense
  license: perl_5
  size: 13136
  timestamp: 1644512391683
- conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
  sha256: 202af1de83b585d36445dc1fda94266697341994d1a3328fabde4989e1b3d07a
  md5: d0d408b1f18883a944376da5cf8101ea
  depends:
  - ptyprocess >=0.5
  - python >=3.9
  license: ISC
  size: 53561
  timestamp: 1733302019362
- conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
  sha256: e2ac3d66c367dada209fc6da43e645672364b9fd5f9d28b9f016e24b81af475b
  md5: 11a9d1d09a3615fc07c3faf79bc0b943
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 11748
  timestamp: 1733327448200
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.3.0-py312h80c1187_0.conda
  sha256: 7c9a8f65a200587bf7a0135ca476f9c472348177338ed8b825ddcc08773fde68
  md5: 7911e727a6c24db662193a960b81b6b2
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42964111
  timestamp: 1751482158083
- conda: https://conda.anaconda.org/conda-forge/osx-64/pillow-11.3.0-py312hd9f36e3_0.conda
  sha256: c80c1e858659beadcd9de16ccb208a319d34cce9a6412731cf2d08dfc1eb86fa
  md5: a3c63eeab0ecca11e93104aebed345fc
  depends:
  - __osx >=10.13
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42486529
  timestamp: 1751482537411
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pillow-11.3.0-py312h50aef2c_0.conda
  sha256: 3d60288e8cfd42e4548c9e5192a285e73f81df2869f69b9d3905849b45d9bd2a
  md5: dddff48655b5cd24a5170a6df979943a
  depends:
  - __osx >=11.0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  size: 42514714
  timestamp: 1751482419501
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.4-h537e5f6_0.conda
  sha256: f1a4bed536f8860b4e67fcd17662884dfa364e515c195c6d2e41dbf70f19263b
  md5: b0674781beef9e302a17c330213ec41a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  license: MIT
  license_family: MIT
  size: 410140
  timestamp: 1753105399719
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.2.0-pyhd8ed1ab_0.conda
  sha256: d72d601e09722c434871c29a102202178fe1fcf031c6290e10fb4a756c1944a3
  md5: 8a9590843af49b36f37ac3dbcf5fc3d9
  depends:
  - narwhals >=1.15.1
  - packaging
  - python >=3.9
  constrains:
  - ipywidgets >=7.6
  license: MIT
  license_family: MIT
  size: 5187885
  timestamp: 1751025216667
- conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
  sha256: a8eb555eef5063bbb7ba06a379fa7ea714f57d9741fe0efdb9442dbbc2cccbcc
  md5: 7da7ccd349dbf6487a7778579d2bb971
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 24246
  timestamp: 1747339794916
- conda: https://conda.anaconda.org/conda-forge/linux-64/polars-1.31.0-default_h70f2ef1_1.conda
  sha256: c3b5c32546ecd37261443f8d614e792e42f07ecd359d1b320d0c6b9ab785f1ba
  md5: 0217d9e4176cf33942996a7ee3afac0e
  depends:
  - polars-default ==1.31.0 py39hf521cc8_1
  license: MIT
  license_family: MIT
  size: 5686
  timestamp: 1752428951262
- conda: https://conda.anaconda.org/conda-forge/osx-64/polars-1.31.0-default_h1ec6524_1.conda
  sha256: 2d471c710ec86be24908663d72c70386f6266d9b99b75193b509dfbbea46d730
  md5: ebcb564b975a39b4b5b91e7eff038b97
  depends:
  - polars-default ==1.31.0 py39hbd2d40b_1
  license: MIT
  license_family: MIT
  size: 5702
  timestamp: 1752428787022
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-1.31.0-default_h13af070_1.conda
  sha256: c09f41c4eb75837ee15071bcee7b4ea892d867f4fd25d71de62c8eed454c2d40
  md5: 9bcc64b1174db66d44d592682309fe97
  depends:
  - polars-default ==1.31.0 py39h31c57e4_1
  license: MIT
  license_family: MIT
  size: 5708
  timestamp: 1752428810220
- conda: https://conda.anaconda.org/conda-forge/linux-64/polars-default-1.31.0-py39hf521cc8_1.conda
  noarch: python
  sha256: cdebbb50896f15490a76a8829408b824f79dc160388c260521a1d2e68302e8b1
  md5: 85f9f61975ba5a8f3d40b477aef457cb
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - libgcc >=14
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - numpy >=1.16.0
  - pyarrow >=7.0.0
  - fastexcel >=0.9
  - openpyxl >=3.0.0
  - xlsx2csv >=0.8.0
  - connectorx >=0.3.2
  - deltalake >=1.0.0
  - pyiceberg >=0.7.1
  - altair >=5.4.0
  - great_tables >=0.8.0
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  size: 28879945
  timestamp: 1752428951262
- conda: https://conda.anaconda.org/conda-forge/osx-64/polars-default-1.31.0-py39hbd2d40b_1.conda
  noarch: python
  sha256: f739d058ee3aab8c10becca650c443aa477769841b44442f4f438af28728319b
  md5: d98635ed0124199d6768b9684cd086fc
  depends:
  - python
  - libcxx >=19
  - __osx >=10.13
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - numpy >=1.16.0
  - pyarrow >=7.0.0
  - fastexcel >=0.9
  - openpyxl >=3.0.0
  - xlsx2csv >=0.8.0
  - connectorx >=0.3.2
  - deltalake >=1.0.0
  - pyiceberg >=0.7.1
  - altair >=5.4.0
  - great_tables >=0.8.0
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 28753260
  timestamp: 1752428787020
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/polars-default-1.31.0-py39h31c57e4_1.conda
  noarch: python
  sha256: 61425d621104bbd4634503207a0657e79a9c6d90d3094be8a858b44d1882fda6
  md5: 72df9afd7d08d2177972102bac5670d7
  depends:
  - python
  - __osx >=11.0
  - libcxx >=19
  - _python_abi3_support 1.*
  - cpython >=3.9
  constrains:
  - numpy >=1.16.0
  - pyarrow >=7.0.0
  - fastexcel >=0.9
  - openpyxl >=3.0.0
  - xlsx2csv >=0.8.0
  - connectorx >=0.3.2
  - deltalake >=1.0.0
  - pyiceberg >=0.7.1
  - altair >=5.4.0
  - great_tables >=0.8.0
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 26278569
  timestamp: 1752428810217
- conda: https://conda.anaconda.org/conda-forge/noarch/pre-commit-4.2.0-pyha770c72_0.conda
  sha256: d0bd8cce5f31ae940934feedec107480c00f67e881bf7db9d50c6fc0216a2ee0
  md5: 17e487cc8b5507cd3abc09398cf27949
  depends:
  - cfgv >=2.0.0
  - identify >=1.0.0
  - nodeenv >=0.11.1
  - python >=3.9
  - pyyaml >=5.1
  - virtualenv >=20.10.0
  license: MIT
  license_family: MIT
  size: 195854
  timestamp: 1742475656293
- conda: https://conda.anaconda.org/bioconda/linux-64/prodigal-gv-2.11.0-h577a1d6_5.tar.bz2
  sha256: d104bac9baced0e55b04f20c4dc4cda4946d362935073aea2c788d7a2fd23445
  md5: 4348916135142cfc3618cb1ad50e98e0
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: GPL-3.0-only
  size: 713443
  timestamp: 1734150468723
- conda: https://conda.anaconda.org/bioconda/osx-64/prodigal-gv-2.11.0-h7f84b70_5.tar.bz2
  sha256: 97d4ca2d7a3c280c3aded99f37ca5326e02b74ff960c9a37021a0b06804593f8
  md5: d8ce915b996a5ab9591538becf21088d
  depends:
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: GPL-3.0-only
  size: 514281
  timestamp: 1733856745501
- conda: https://conda.anaconda.org/bioconda/osx-arm64/prodigal-gv-2.11.0-hba9b596_5.tar.bz2
  sha256: 40f9ad7fd9716fce0764ff3959e5ef9c8505eb4a135814d0c3a38558dd70f18d
  md5: dbcc7733e897d699f79c937a01271292
  depends:
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: GPL-3.0-only
  size: 544974
  timestamp: 1733867040358
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.1-pyhd8ed1ab_0.conda
  sha256: 454e2c0ef14accc888dd2cd2e8adb8c6a3a607d2d3c2f93962698b5718e6176d
  md5: c64b77ccab10b822722904d889fa83b5
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 52641
  timestamp: 1748896836631
- conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
  sha256: ebc1bb62ac612af6d40667da266ff723662394c0ca78935340a5b5c14831227b
  md5: d17ae9db4dc594267181bd199bf9a551
  depends:
  - python >=3.9
  - wcwidth
  constrains:
  - prompt_toolkit 3.0.51
  license: BSD-3-Clause
  license_family: BSD
  size: 271841
  timestamp: 1744724188108
- conda: https://conda.anaconda.org/bioconda/linux-64/proteinortho-6.3.6-h2b77389_0.tar.bz2
  sha256: 3adef030728eb7fa1d97d9598ac3750d93f81655a62b7b55ac613ddfabaacd8e
  md5: 7018231bf6f2a318a3362fa924c917cc
  depends:
  - _openmp_mutex >=4.5
  - diamond >=0.9.29
  - libblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - libgomp
  - liblapacke >=3.9.0,<4.0a0
  - libstdcxx >=13
  - perl
  - python
  license: GPL-3.0-only
  license_family: GPL
  size: 381239
  timestamp: 1749712166238
- conda: https://conda.anaconda.org/bioconda/osx-64/proteinortho-6.3.6-hdcb8ee1_0.tar.bz2
  sha256: 1b740f6c6eba7a932546e34563b90e7732c15ccd3f3aa33d3c0cb2d40575cb3a
  md5: 994998e929a5e12c8ffbd84a8d78eae0
  depends:
  - diamond >=0.9.29
  - libblas >=3.9.0,<4.0a0
  - libcxx >=18
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - libgfortran5 >=14.2.0
  - liblapacke >=3.9.0,<4.0a0
  - llvm-openmp >=18.1.8
  - perl
  - python
  license: GPL-3.0-only
  license_family: GPL
  size: 350578
  timestamp: 1749712917213
- conda: https://conda.anaconda.org/bioconda/osx-arm64/proteinortho-6.3.6-hd1e0bca_0.tar.bz2
  sha256: faf53aa465e33ec7623f025532e8e2f2c2a070653e9890e020c046e0c972f782
  md5: 1d5feb884fef2a02f6d25c430888183f
  depends:
  - diamond >=0.9.29
  - libblas >=3.9.0,<4.0a0
  - libcxx >=18
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - libgfortran5 >=14.2.0
  - liblapacke >=3.9.0,<4.0a0
  - llvm-openmp >=18.1.8
  - perl
  - python
  license: GPL-3.0-only
  license_family: GPL
  size: 383021
  timestamp: 1749711988423
- conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py312h66e93f0_0.conda
  sha256: 158047d7a80e588c846437566d0df64cec5b0284c7184ceb4f3c540271406888
  md5: 8e30db4239508a538e4a3b3cdf5b9616
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 466219
  timestamp: 1740663246825
- conda: https://conda.anaconda.org/conda-forge/osx-64/psutil-7.0.0-py312h01d7ebd_0.conda
  sha256: bdfa40a1ef3a80c3bec425a5ed507ebda2bdebce2a19bccb000db9d5c931750c
  md5: fcad6b89f4f7faa999fa4d887eab14ba
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 473946
  timestamp: 1740663466925
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/psutil-7.0.0-py312hea69d52_0.conda
  sha256: cb11dcb39b2035ef42c3df89b5a288744b5dcb5a98fb47385760843b1d4df046
  md5: 0f461bd37cb428dc20213a08766bb25d
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 476376
  timestamp: 1740663381256
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/osx-64/pthread-stubs-0.4-h00291cd_1002.conda
  sha256: 05944ca3445f31614f8c674c560bca02ff05cb51637a96f665cb2bbe496099e5
  md5: 8bcf980d2c6b17094961198284b8e862
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 8364
  timestamp: 1726802331537
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pthread-stubs-0.4-hd74edd7_1002.conda
  sha256: 8ed65e17fbb0ca944bfb8093b60086e3f9dd678c3448b5de212017394c247ee3
  md5: 415816daf82e0b23a736a069a75e9da7
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 8381
  timestamp: 1726802424786
- conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
  sha256: a7713dfe30faf17508ec359e0bc7e0983f5d94682492469bd462cdaae9c64d83
  md5: 7d9daffbb8d8e0af0f769dbbcd173a54
  depends:
  - python >=3.9
  license: ISC
  size: 19457
  timestamp: 1733302371990
- conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
  sha256: 71bd24600d14bb171a6321d523486f6a06f855e75e547fa0cb2a0953b02047f0
  md5: 3bfdfb8dbcdc4af1ae3f9a8eb3948f04
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 16668
  timestamp: 1733569518868
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.2-pyhd8ed1ab_0.conda
  sha256: 5577623b9f6685ece2697c6eb7511b4c9ac5fb607c9babc2646c811b428fd46a
  md5: 6b6ece66ebcae2d5f326c77ef2c5a066
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  size: 889287
  timestamp: 1750615908735
- conda: https://conda.anaconda.org/conda-forge/noarch/pymdown-extensions-10.16-pyhd8ed1ab_0.conda
  sha256: 7465d67daa980999606138d74631563f5c233624cf5d65fc3f1f7210fce91b64
  md5: 79dbb1bfe734d8e8b36ca328a63fb4de
  depends:
  - markdown >=3.6
  - python >=3.9
  - pyyaml
  license: MIT
  license_family: MIT
  size: 171431
  timestamp: 1750571864207
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-core-11.1-py312h3f2cce9_0.conda
  sha256: d4376eba59828c0134a439d5c82ee1d7a2dcd4f7c80878859b363865979b3f56
  md5: 5cdd230ab8467ca37570cd09a3977e17
  depends:
  - __osx >=10.13
  - libffi >=3.4.6,<3.5.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - setuptools
  license: MIT
  license_family: MIT
  size: 484609
  timestamp: 1750207854345
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-core-11.1-py312h4c66426_0.conda
  sha256: d4b1ae7f925720c1a6643c03199c6a47ba6a536bfd630f522baa5fe6ebf4a786
  md5: 02247b8a9ba52a15a53edd6d4cf9dac4
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - setuptools
  license: MIT
  license_family: MIT
  size: 474838
  timestamp: 1750207878592
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyobjc-framework-cocoa-11.1-py312h2365019_0.conda
  sha256: df309c1fd5a015d92c687200a10661a63955387620f61b1dd17a151d4a6ad4d1
  md5: dc83fce82c147af35c199348ce4938a6
  depends:
  - __osx >=10.13
  - libffi >=3.4.6,<3.5.0a0
  - pyobjc-core 11.1.*
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 380589
  timestamp: 1750225380233
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyobjc-framework-cocoa-11.1-py312hb9d441b_0.conda
  sha256: a6f262fe5706c73dce7ca7fbec9a055fc225422ad8d7fc45dd66ad9dddb0afe3
  md5: 5b7a58b273bca2c67dd8ddaea92e404e
  depends:
  - __osx >=11.0
  - libffi >=3.4.6,<3.5.0a0
  - pyobjc-core 11.1.*
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 386128
  timestamp: 1750225477437
- conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
  sha256: b92afb79b52fcf395fd220b29e0dd3297610f2059afac45298d44e00fcbf23b6
  md5: 513d3c262ee49b54a8fec85c5bc99764
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 95988
  timestamp: 1743089832359
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.9.1-py312hdb827e4_0.conda
  sha256: 782c46d57daf2e027cd4d6a7c440ccecf09aca34e200d209b1d1a4ebb0548789
  md5: 843ad8ae4523f47a7f636f576750c487
  depends:
  - __glibc >=2.17,<3.0.a0
  - libclang13 >=20.1.6
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libopengl >=1.7.0,<2.0a0
  - libstdcxx >=13
  - libxml2 >=2.13.8,<2.14.0a0
  - libxslt >=1.1.39,<2.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - qt6-main 6.9.1.*
  - qt6-main >=6.9.1,<6.10.0a0
  license: LGPL-3.0-only
  license_family: LGPL
  size: 10133664
  timestamp: 1749047343971
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.4.1-pyhd8ed1ab_0.conda
  sha256: 93e267e4ec35353e81df707938a6527d5eb55c97bf54c3b87229b69523afb59d
  md5: a49c2283f24696a7b30367b7346a0144
  depends:
  - colorama >=0.4
  - exceptiongroup >=1
  - iniconfig >=1
  - packaging >=20
  - pluggy >=1.5,<2
  - pygments >=2.7.2
  - python >=3.9
  - tomli >=1
  constrains:
  - pytest-faulthandler >=2
  license: MIT
  license_family: MIT
  size: 276562
  timestamp: 1750239526127
- conda: https://conda.anaconda.org/conda-forge/noarch/pytest-cov-6.2.1-pyhd8ed1ab_0.conda
  sha256: 3a9fc07be76bc67aef355b78816b5117bfe686e7d8c6f28b45a1f89afe104761
  md5: ce978e1b9ed8b8d49164e90a5cdc94cd
  depends:
  - coverage >=7.5
  - pytest >=4.6
  - python >=3.9
  - toml
  license: MIT
  license_family: MIT
  size: 28216
  timestamp: 1749778064293
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.11-h9e4cc4f_0_cpython.conda
  sha256: 6cca004806ceceea9585d4d655059e951152fc774a471593d4f5138e6a54c81d
  md5: 94206474a5608243a10c92cefbe0908f
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  size: 31445023
  timestamp: 1749050216615
- conda: https://conda.anaconda.org/conda-forge/osx-64/python-3.12.11-h9ccd52b_0_cpython.conda
  sha256: ebda5b5e8e25976013fdd81b5ba253705b076741d02bdc8ab32763f2afb2c81b
  md5: 06049132ecd09d0c1dc3d54d93cf1d5d
  depends:
  - __osx >=10.13
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  size: 13571569
  timestamp: 1749049058713
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-3.12.11-hc22306f_0_cpython.conda
  sha256: cde8b944c2dc378a5afbc48028d0843583fd215493d5885a80f1b41de085552f
  md5: 9207ebad7cfbe2a4af0702c92fd031c4
  depends:
  - __osx >=11.0
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - liblzma >=5.8.1,<6.0a0
  - libsqlite >=3.50.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  size: 13009234
  timestamp: 1749048134449
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhe01879c_2.conda
  sha256: d6a17ece93bbd5139e02d2bd7dbfa80bee1a4261dced63f65f679121686bf664
  md5: 5b8d21249ff20967101ffa321cab24e8
  depends:
  - python >=3.9
  - six >=1.5
  - python
  license: Apache-2.0
  license_family: APACHE
  size: 233310
  timestamp: 1751104122689
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-duckdb-1.3.2-py312h1289d80_0.conda
  sha256: 1d12c1bea202d5f2101193e97674fc83aa7c12841a44eae40683bdb0823a5617
  md5: 2fb46eab88950d78d327068acfe83a55
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libstdcxx >=14
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 24487971
  timestamp: 1752087127423
- conda: https://conda.anaconda.org/conda-forge/osx-64/python-duckdb-1.3.2-py312h462f358_0.conda
  sha256: 8aeb03b325bf197c5c99c2e776a4c50c302ec5bf526a5065489f099df18ae280
  md5: 68011b2d7534f904067de7b9f7deb59c
  depends:
  - __osx >=10.13
  - libcxx >=19
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 21703259
  timestamp: 1752086508374
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/python-duckdb-1.3.2-py312h6b01ec3_0.conda
  sha256: e9087a44879801f18fd7108219656e13e73779b97592cd783f8b35ade54cb19c
  md5: 50457ff8b7a7c44dd264d164fe702a26
  depends:
  - __osx >=11.0
  - libcxx >=19
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 19928035
  timestamp: 1752086901242
- conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
  sha256: 1b09a28093071c1874862422696429d0d35bd0b8420698003ac004746c5e82a2
  md5: 38e34d2d1d9dca4fb2b9a0a04f604e2c
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 226259
  timestamp: 1733236073335
- conda: https://conda.anaconda.org/conda-forge/noarch/python-gil-3.12.11-hd8ed1ab_0.conda
  sha256: b8afeaefe409d61fa4b68513b25a66bb17f3ca430d67cfea51083c7bfbe098ef
  md5: 859c6bec94cd74119f12b961aba965a8
  depends:
  - cpython 3.12.11.*
  - python_abi * *_cp312
  license: Python-2.0
  size: 45836
  timestamp: 1749047798827
- conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
  sha256: 4790787fe1f4e8da616edca4acf6a4f8ed4e7c6967aa31b920208fc8f95efcca
  md5: a61bf9ec79426938ff785eb69dbb1960
  depends:
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  size: 13383
  timestamp: 1677079727691
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.12-8_cp312.conda
  build_number: 8
  sha256: 80677180dd3c22deb7426ca89d6203f1c7f1f256f2d5a94dc210f6e758229809
  md5: c3efd25ac4d74b1584d2f7a57195ddf1
  constrains:
  - python 3.12.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  size: 6958
  timestamp: 1752805918820
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py312h178313f_2.conda
  sha256: 159cba13a93b3fe084a1eb9bda0a07afc9148147647f0d437c3c3da60980503b
  md5: cf2485f39740de96e2a7f2bb18ed2fee
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 206903
  timestamp: 1737454910324
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyyaml-6.0.2-py312h3520af0_2.conda
  sha256: de96d83b805dba03422d39e855fb33cbeedc8827235d6f76407a3b42dc085910
  md5: 4a2d83ac55752681d54f781534ddd209
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 193577
  timestamp: 1737454858212
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyyaml-6.0.2-py312h998013c_2.conda
  sha256: ad225ad24bfd60f7719709791345042c3cb32da1692e62bd463b084cf140e00d
  md5: 68149ed4d4e9e1c42d2ba1f27f08ca96
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  size: 192148
  timestamp: 1737454886351
- conda: https://conda.anaconda.org/conda-forge/noarch/pyyaml-env-tag-1.1-pyhd8ed1ab_0.conda
  sha256: 69ab63bd45587406ae911811fc4d4c1bf972d643fa57a009de7c01ac978c4edd
  md5: e8e53c4150a1bba3b160eacf9d53a51b
  depends:
  - python >=3.9
  - pyyaml
  license: MIT
  license_family: MIT
  size: 11137
  timestamp: 1747237061448
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-27.0.1-py312h6748674_0.conda
  sha256: 31fac3fe50d9a18a89f92483db4bdb2995e2126d237aaec92c367bad9efe0896
  md5: 14f393a112e2ac0e87d257a25ff23ed2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=14
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 379013
  timestamp: 1754238168795
- conda: https://conda.anaconda.org/conda-forge/osx-64/pyzmq-27.0.1-py312hbb7883b_0.conda
  sha256: 4fc9c8a606d88cddcc59432db3bd28dd180f9538c9f9c6cb1186b384a0fb0040
  md5: 89bf2bed3fbb0d2b489267e22d27ed5f
  depends:
  - __osx >=10.13
  - libcxx >=19
  - libsodium >=1.0.20,<1.0.21.0a0
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 364858
  timestamp: 1754238316688
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/pyzmq-27.0.1-py312h211b278_0.conda
  sha256: ed8712fd5be75843c1ae99fc8feb44d53bdfef259d13549fddfbf424135e03a3
  md5: 4205cddbceb48fd4d006ca2f9689e588
  depends:
  - __osx >=11.0
  - libcxx >=19
  - libsodium >=1.0.20,<1.0.21.0a0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 358958
  timestamp: 1754238387025
- conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
  sha256: 776363493bad83308ba30bcb88c2552632581b143e8ee25b1982c8c743e73abc
  md5: 353823361b1d27eb3960efb076dfcaf6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LicenseRef-Qhull
  size: 552937
  timestamp: 1720813982144
- conda: https://conda.anaconda.org/conda-forge/osx-64/qhull-2020.2-h3c5361c_5.conda
  sha256: 79d804fa6af9c750e8b09482559814ae18cd8df549ecb80a4873537a5a31e06e
  md5: dd1ea9ff27c93db7c01a7b7656bd4ad4
  depends:
  - __osx >=10.13
  - libcxx >=16
  license: LicenseRef-Qhull
  size: 528122
  timestamp: 1720814002588
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/qhull-2020.2-h420ef59_5.conda
  sha256: 873ac689484262a51fd79bc6103c1a1bedbf524924d7f0088fb80703042805e4
  md5: 6483b1f59526e05d7d894e466b5b6924
  depends:
  - __osx >=11.0
  - libcxx >=16
  license: LicenseRef-Qhull
  size: 516376
  timestamp: 1720814307311
- conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.9.1-h6ac528c_2.conda
  sha256: 8795462e675b7235ad3e01ff3367722a37915c7084d0fb897b328b7e28a358eb
  md5: 34ccdb55340a25761efbac1ff1504091
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - dbus >=1.16.2,<2.0a0
  - double-conversion >=3.3.1,<3.4.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - harfbuzz >=11.0.1
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libclang-cpp20.1 >=20.1.8,<20.2.0a0
  - libclang13 >=20.1.8
  - libcups >=2.3.3,<2.4.0a0
  - libdrm >=2.4.125,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=14
  - libgl >=1.7.0,<2.0a0
  - libglib >=2.84.2,<3.0a0
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libllvm20 >=20.1.8,<20.2.0a0
  - libpng >=1.6.50,<1.7.0a0
  - libpq >=17.5,<18.0a0
  - libsqlite >=3.50.3,<4.0a0
  - libstdcxx >=14
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.6.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxkbcommon >=1.10.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.1,<4.0a0
  - pcre2 >=10.45,<10.46.0a0
  - wayland >=1.24.0,<2.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  - xcb-util-cursor >=0.1.5,<0.2.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-keysyms >=0.4.1,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  - xcb-util-wm >=0.4.2,<0.5.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.6,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcomposite >=0.4.6,<1.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - xorg-libxdamage >=1.1.6,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrandr >=1.5.4,<2.0a0
  - xorg-libxtst >=1.2.5,<2.0a0
  - xorg-libxxf86vm >=1.1.6,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - qt 6.9.1
  license: LGPL-3.0-only
  size: 53080009
  timestamp: 1753420196625
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/osx-64/readline-8.2-h7cca4af_2.conda
  sha256: 53017e80453c4c1d97aaf78369040418dea14cf8f46a2fa999f31bd70b36c877
  md5: 342570f8e02f2f022147a7f841475784
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 256712
  timestamp: 1740379577668
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/readline-8.2-h1d1bf99_2.conda
  sha256: 7db04684d3904f6151eff8673270922d31da1eea7fa73254d01c437f49702e34
  md5: 63ef3f6e6d6d5c589e64f11263dc5676
  depends:
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  size: 252359
  timestamp: 1740379663071
- conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
  sha256: e20909f474a6cece176dfc0dc1addac265deb5fa92ea90e975fbca48085b20c3
  md5: 9140f1c09dd5489549c6a33931b943c7
  depends:
  - attrs >=22.2.0
  - python >=3.9
  - rpds-py >=0.7.0
  - typing_extensions >=4.4.0
  - python
  license: MIT
  license_family: MIT
  size: 51668
  timestamp: 1737836872415
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.4-pyhd8ed1ab_0.conda
  sha256: 9866aaf7a13c6cfbe665ec7b330647a0fb10a81e6f9b8fee33642232a1920e18
  md5: f6082eae112814f1447b56a5e1f6ed05
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  size: 59407
  timestamp: 1749498221996
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
  sha256: 2e4372f600490a6e0b3bac60717278448e323cab1c0fecd5f43f7c56535a99c5
  md5: 36de09a8d3e5d5e6f4ee63af49e59706
  depends:
  - python >=3.9
  - six
  license: MIT
  license_family: MIT
  size: 10209
  timestamp: 1733600040800
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
  sha256: 2a5b495a1de0f60f24d8a74578ebc23b24aa53279b1ad583755f223097c41c37
  md5: 912a71cc01012ee38e6b90ddd561e36f
  depends:
  - python
  license: MIT
  license_family: MIT
  size: 7818
  timestamp: 1598024297745
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3987-syntax-1.1.0-pyhe01879c_1.conda
  sha256: 70001ac24ee62058557783d9c5a7bbcfd97bd4911ef5440e3f7a576f9e43bc92
  md5: 7234f99325263a5af6d4cd195035e8f2
  depends:
  - python >=3.9
  - lark >=1.2.2
  - python
  license: MIT
  license_family: MIT
  size: 22913
  timestamp: 1752876729969
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.1.0-pyhe01879c_0.conda
  sha256: 3bda3cd6aa2ca8f266aeb8db1ec63683b4a7252d7832e8ec95788fb176d0e434
  md5: c41e49bd1f1479bed6c6300038c5466e
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.9
  - typing_extensions >=4.0.0,<5.0.0
  - python
  license: MIT
  size: 201098
  timestamp: 1753436991345
- conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.27.0-py312h868fb18_0.conda
  sha256: cfc9c79f0e2658754b02efb890fe3c835d865ed0535155787815ae16e56dbe9c
  md5: 3d3d11430ec826a845a0e9d6ccefa294
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  - python_abi 3.12.* *_cp312
  constrains:
  - __glibc >=2.17
  license: MIT
  size: 388899
  timestamp: 1754570135763
- conda: https://conda.anaconda.org/conda-forge/osx-64/rpds-py-0.27.0-py312h00ff6fd_0.conda
  sha256: 79698e8fa42df6c28e1082dbafdf9ccb48e68bfc69b324b65d846af88c6254c9
  md5: 520e0ccc082eea6649ff7acf18852e51
  depends:
  - python
  - __osx >=10.13
  - python_abi 3.12.* *_cp312
  constrains:
  - __osx >=10.13
  license: MIT
  size: 368889
  timestamp: 1754569971769
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/rpds-py-0.27.0-py312h6f58b40_0.conda
  sha256: 0a14b856d41b4ef51a4c67fd8200b18c1c21ba0f252a2e3f9f85678149e08141
  md5: ccbe846733e149a842df80f53f66ca72
  depends:
  - python
  - __osx >=11.0
  - python 3.12.* *_cpython
  - python_abi 3.12.* *_cp312
  constrains:
  - __osx >=11.0
  license: MIT
  size: 357078
  timestamp: 1754569997063
- conda: https://conda.anaconda.org/bioconda/linux-64/rpsbproc-0.5.0-hd6d6fdc_3.tar.bz2
  sha256: 00d2231659f0d878cec80c4e40c555a840342e8e6add237e969654bfa0e33b00
  md5: 47bbd5d7e01700d81ce16888350cda62
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libsqlite >=3.49.1,<4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: Public Domain
  size: 6555182
  timestamp: 1743100884009
- conda: https://conda.anaconda.org/bioconda/osx-64/rpsbproc-0.5.0-heca6186_3.tar.bz2
  sha256: d1acdb7f1ba682f3ff0ee26effb7d8a32df73acbdd2348b2f6a795d7947aa218
  md5: bad1baa4ec5fb86237ab934b6283d15f
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libcxx >=18
  - libsqlite >=3.49.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  license: Public Domain
  size: 4975729
  timestamp: 1743104040637
- conda: https://conda.anaconda.org/bioconda/osx-arm64/rpsbproc-0.5.0-hf8bb5b5_3.tar.bz2
  sha256: e56c776f6c68d1e8ce22d8663aa2df5f763e9e0bd727429aa0b0c191906d7f1d
  md5: be4c9f7fcf4b875518e03341995e786b
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libcxx >=18
  - libsqlite >=3.49.1,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  license: Public Domain
  size: 4701550
  timestamp: 1743099954895
- conda: https://conda.anaconda.org/conda-forge/linux-64/ruff-0.12.5-hf9daec2_0.conda
  noarch: python
  sha256: e0383ea982545b0836771b58cd6d5e516f722d02b899f5bf325a54c8f6ef73b4
  md5: 37a142ca01da7f87652d55a1fb5043e8
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=14
  constrains:
  - __glibc >=2.17
  license: MIT
  size: 10477480
  timestamp: 1753401049977
- conda: https://conda.anaconda.org/conda-forge/osx-64/ruff-0.12.5-h6cc4cfe_0.conda
  noarch: python
  sha256: 13bc134e829fb3bd716d3028c6aa7938b6fcdaae620000afea83a0e7a4e2e2fd
  md5: 5f63b268b1ca853a3894f2f697b0f5ce
  depends:
  - python
  - __osx >=10.13
  constrains:
  - __osx >=10.13
  license: MIT
  size: 10494874
  timestamp: 1753401117063
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ruff-0.12.5-h575f11b_0.conda
  noarch: python
  sha256: 18eb56cfd7c3d5f07ea2459c9b94ac1cfc5ded2072385ec3b784eaf0d00baece
  md5: 950f9ba560c91bb5785e5f388774799c
  depends:
  - python
  - __osx >=11.0
  constrains:
  - __osx >=11.0
  license: MIT
  size: 9692798
  timestamp: 1753401120925
- conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.7.1-py312h4f0b9e3_0.conda
  sha256: c87194d7a0659493aa8ca9007bba2a4a8965e60037c396cd2e08fc1b5c91548b
  md5: 7f96df096abbe0064f0ec5060c1d2af4
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - joblib >=1.2.0
  - libgcc >=14
  - libstdcxx >=14
  - numpy >=1.22.0
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - scipy >=1.8.0
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 9685421
  timestamp: 1752826143141
- conda: https://conda.anaconda.org/conda-forge/osx-64/scikit-learn-1.7.1-py312hf34d0c2_0.conda
  sha256: 64a309bfe938f32169f1ff5160e3a1f0387020a072cc4d1d998ed1342c65325c
  md5: 702f651c3e601a88700bcce18f31b0c9
  depends:
  - __osx >=10.13
  - joblib >=1.2.0
  - libcxx >=19
  - llvm-openmp >=19.1.7
  - numpy >=1.22.0
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - scipy >=1.8.0
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 9083864
  timestamp: 1752826115451
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/scikit-learn-1.7.1-py312h54d6233_0.conda
  sha256: c1a079efc29fdb840f9a2b53ee44940aafbe81b4f1845c1281aa9da77b2c4ce5
  md5: d384e66a54996cc54614fdd111489d6a
  depends:
  - __osx >=11.0
  - joblib >=1.2.0
  - libcxx >=19
  - llvm-openmp >=19.1.7
  - numpy >=1.22.0
  - numpy >=1.23,<3
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - scipy >=1.8.0
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  size: 8931629
  timestamp: 1752826246695
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.16.0-py312hf734454_0.conda
  sha256: 8406e26bf853e699b1ea97792f63987808783ff4ab6ddeff9cf1ec0b9d1aa342
  md5: 7513ac56209d27a85ffa1582033f10a8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 16847456
  timestamp: 1751148548291
- conda: https://conda.anaconda.org/conda-forge/osx-64/scipy-1.16.0-py312hd0c0319_0.conda
  sha256: 4aab814a523927c14062a008fd2c42b91961a6e9d9adc54a18f9b49ffc058caf
  md5: 713d61abff10ba1c063cf931ca5bac7d
  depends:
  - __osx >=10.13
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=18
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - libgfortran5 >=14.2.0
  - liblapack >=3.9.0,<4.0a0
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 15221024
  timestamp: 1751148523429
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/scipy-1.16.0-py312hcedbd36_0.conda
  sha256: d0033c0414910c2bb6005e005e0df266a6c21e1928efec2df3251736245c1258
  md5: b3ab5755feaabeaf889063663790eb25
  depends:
  - __osx >=11.0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libcxx >=18
  - libgfortran 5.*
  - libgfortran5 >=13.3.0
  - libgfortran5 >=14.2.0
  - liblapack >=3.9.0,<4.0a0
  - numpy <2.6
  - numpy >=1.23,<3
  - numpy >=1.25.2
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 13846609
  timestamp: 1751148522848
- conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-0.13.2-hd8ed1ab_3.conda
  noarch: python
  sha256: ea29a69b14dd6be5cdeeaa551bf50d78cafeaf0351e271e358f9b820fcab4cb0
  md5: 62afb877ca2c2b4b6f9ecb37320085b6
  depends:
  - seaborn-base 0.13.2 pyhd8ed1ab_3
  - statsmodels >=0.12
  license: BSD-3-Clause
  license_family: BSD
  size: 6876
  timestamp: 1733730113224
- conda: https://conda.anaconda.org/conda-forge/noarch/seaborn-base-0.13.2-pyhd8ed1ab_3.conda
  sha256: f209c9c18187570b85ec06283c72d64b8738f825b1b82178f194f4866877f8aa
  md5: fd96da444e81f9e6fcaac38590f3dd42
  depends:
  - matplotlib-base >=3.4,!=3.6.1
  - numpy >=1.20,!=1.24.0
  - pandas >=1.2
  - python >=3.9
  - scipy >=1.7
  constrains:
  - seaborn =0.13.2=*_3
  license: BSD-3-Clause
  license_family: BSD
  size: 227843
  timestamp: 1733730112409
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
  sha256: 00926652bbb8924e265caefdb1db100f86a479e8f1066efe395d5552dde54d02
  md5: 938c8de6b9de091997145b3bf25cdbf9
  depends:
  - __linux
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 22736
  timestamp: 1733322148326
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh31c8845_1.conda
  sha256: 5282eb5b462502c38df8cb37cd1542c5bbe26af2453a18a0a0602d084ca39f53
  md5: e67b1b1fa7a79ff9e8e326d0caf55854
  depends:
  - __osx
  - pyobjc-framework-cocoa
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 23100
  timestamp: 1733322309409
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-80.9.0-pyhff2d567_0.conda
  sha256: 972560fcf9657058e3e1f97186cc94389144b46dbdf58c807ce62e83f977e863
  md5: 4de79c071274a53dcaf2a8c749d1499e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 748788
  timestamp: 1748804951958
- conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
  sha256: 0557c090913aa63cdbe821dbdfa038a321b488e22bc80196c4b3b1aace4914ef
  md5: 7c3c2a0f3ebdea2bbc35538d162b43bf
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 14462
  timestamp: 1733301007770
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhe01879c_1.conda
  sha256: 458227f759d5e3fcec5d9b7acce54e10c9e1f4f4b7ec978f3bfd54ce4ee9853d
  md5: 3339e3b65d58accf4ca4fb8748ab16b3
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 18455
  timestamp: 1753199211006
- conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
  sha256: c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012
  md5: bf7a226e58dfb8346c70df36065d86c9
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  size: 15019
  timestamp: 1733244175724
- conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
  sha256: 7518506cce9a736042132f307b3f4abce63bf076f5fb07c1f4e506c0b214295a
  md5: fb32097c717486aa34b38a9db57eb49e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 37773
  timestamp: 1746563720271
- conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
  sha256: 570da295d421661af487f1595045760526964f41471021056e993e73089e9c41
  md5: b1b505328da7a6b246787df4b5a49fbc
  depends:
  - asttokens
  - executing
  - pure_eval
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 26988
  timestamp: 1733569565672
- conda: https://conda.anaconda.org/conda-forge/linux-64/statsmodels-0.14.5-py312h8b63200_0.conda
  sha256: 71af2d8efae963c83f9cd49f4648087d0acd41a58972a5bd7b097273b895ed54
  md5: d3588408248f78db333a5b019a4ca696
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  size: 12062670
  timestamp: 1751917720541
- conda: https://conda.anaconda.org/conda-forge/osx-64/statsmodels-0.14.5-py312h34a05c3_0.conda
  sha256: 5620ebca940ce29415489f3006c710a925d7f730879910b3071d2277cb2375e8
  md5: d1782681f1d944cc1e8712591415304a
  depends:
  - __osx >=10.13
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  size: 11749728
  timestamp: 1751918146123
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/statsmodels-0.14.5-py312hcde60ef_0.conda
  sha256: 911a6fcfa2c04de64d03378e705a0678bf511f25aa0f5f9c8e4570ccb071b241
  md5: 79208bf3a11cedbe7dd53a6798cb88d2
  depends:
  - __osx >=11.0
  - numpy <3,>=1.22.3
  - numpy >=1.23,<3
  - packaging >=21.3
  - pandas !=2.1.0,>=1.4
  - patsy >=0.5.6
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - scipy !=1.9.2,>=1.8
  license: BSD-3-Clause
  license_family: BSD
  size: 11703160
  timestamp: 1751918258652
- conda: https://conda.anaconda.org/conda-forge/noarch/sysroot_linux-64-2.28-h4ee821c_8.conda
  sha256: 0053c17ffbd9f8af1a7f864995d70121c292e317804120be4667f37c92805426
  md5: 1bad93f0aa428d618875ef3a588a889e
  depends:
  - __glibc >=2.28
  - kernel-headers_linux-64 4.18.0 he073ed8_8
  - tzdata
  license: LGPL-2.0-or-later AND LGPL-2.0-or-later WITH exceptions AND GPL-2.0-or-later
  license_family: GPL
  size: 24210909
  timestamp: 1752669140965
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
  sha256: b300557c0382478cf661ddb520263508e4b3b5871b471410450ef2846e8c352c
  md5: efba281bbdae5f6b0a1d53c6d4a97c93
  depends:
  - __linux
  - ptyprocess
  - python >=3.8
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  size: 22452
  timestamp: 1710262728753
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh31c8845_0.conda
  sha256: 4daae56fc8da17784578fbdd064f17e3b3076b394730a14119e571707568dc8a
  md5: 00b54981b923f5aefcd5e8547de056d5
  depends:
  - __osx
  - ptyprocess
  - python >=3.8
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  size: 22717
  timestamp: 1710265922593
- conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
  sha256: 6016672e0e72c4cf23c0cf7b1986283bd86a9c17e8d319212d78d8e9ae42fdfd
  md5: 9d64911b31d57ca443e9f1e36b04385f
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 23869
  timestamp: 1741878358548
- conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
  sha256: cad582d6f978276522f84bd209a5ddac824742fe2d452af6acf900f8650a73a2
  md5: f1acf5fdefa8300de697982bcb1761c9
  depends:
  - python >=3.5
  - webencodings >=0.4
  license: BSD-3-Clause
  license_family: BSD
  size: 28285
  timestamp: 1729802975370
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/osx-64/tk-8.6.13-hf689a15_2.conda
  sha256: b24468006a96b71a5f4372205ea7ec4b399b0f2a543541e86f883de54cd623fc
  md5: 9864891a6946c2fe037c02fca7392ab4
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3259809
  timestamp: 1748387843735
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tk-8.6.13-h892fb3f_2.conda
  sha256: cb86c522576fa95c6db4c878849af0bccfd3264daf0cc40dd18e7f4a7bfced0e
  md5: 7362396c170252e7b7b0c8fb37fe9c78
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  size: 3125538
  timestamp: 1748388189063
- conda: https://conda.anaconda.org/conda-forge/noarch/toml-0.10.2-pyhd8ed1ab_1.conda
  sha256: 34f3a83384ac3ac30aefd1309e69498d8a4aa0bf2d1f21c645f79b180e378938
  md5: b0dd904de08b7db706167240bf37b164
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22132
  timestamp: 1734091907682
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
  sha256: 18636339a79656962723077df9a56c0ac7b8a864329eb8f847ee3d38495b863e
  md5: ac944244f1fed2eb49bae07193ae8215
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 19167
  timestamp: 1733256819729
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py312h66e93f0_0.conda
  sha256: c96be4c8bca2431d7ad7379bad94ed6d4d25cd725ae345540a531d9e26e148c9
  md5: c532a6ee766bed75c4fa0c39e959d132
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 850902
  timestamp: 1748003427956
- conda: https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.1-py312h01d7ebd_0.conda
  sha256: 6e97d6785c466ddd0fe3dad3aa54db6434824bcab40f7490e90943018560bf67
  md5: 62b3f3d78cb285b2090024e2a1e795f7
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 850340
  timestamp: 1748003643552
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/tornado-6.5.1-py312hea69d52_0.conda
  sha256: 02835bf9f49a7c6f73622614be67dc20f9b5c2ce9f663f427150dc0579007daa
  md5: 375a5a90946ff09cd98b9cf5b833023c
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 851614
  timestamp: 1748003575892
- conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
  sha256: 11e2c85468ae9902d24a27137b6b39b4a78099806e551d390e394a8c34b48e40
  md5: 9efbfdc37242619130ea42b1cc4ed861
  depends:
  - colorama
  - python >=3.9
  license: MPL-2.0 or MIT
  size: 89498
  timestamp: 1735661472632
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.16.0-pyh167b9f4_0.conda
  sha256: 1ca70f0c0188598f9425a947afb74914a068bee4b7c4586eabb1c3b02fbf669f
  md5: 985cc086b73bda52b2f8d66dcda460a1
  depends:
  - typer-slim-standard ==0.16.0 hf964461_0
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  size: 77232
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.16.0-pyhe01879c_0.conda
  sha256: 54f859ddf5d3216fb602f54990c3ccefc65a30d1d98c400b998e520310630df3
  md5: 0d0a6c08daccb968c8c8fa93070658e2
  depends:
  - python >=3.9
  - click >=8.0.0
  - typing_extensions >=3.7.4.3
  - python
  constrains:
  - typer 0.16.0.*
  - rich >=10.11.0
  - shellingham >=1.3.0
  license: MIT
  license_family: MIT
  size: 46798
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.16.0-hf964461_0.conda
  sha256: c35a0b232e9751ac871b733d4236eee887f64c3b1539ba86aecf175c3ac3dc02
  md5: c8fb6ddb4f5eb567d049f85b3f0c8019
  depends:
  - typer-slim ==0.16.0 pyhe01879c_0
  - rich
  - shellingham
  license: MIT
  license_family: MIT
  size: 5271
  timestamp: 1748304246569
- conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250809-pyhd8ed1ab_0.conda
  sha256: e54a82e474f4f4b6988c6c7186e5def628c840fca81f5d103e9f78f01d5fead1
  md5: 63a644e158c4f8eeca0d1290ac25e0cc
  depends:
  - python >=3.9
  license: Apache-2.0 AND MIT
  size: 24646
  timestamp: 1754722843717
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.14.1-h4440ef1_0.conda
  sha256: 349951278fa8d0860ec6b61fcdc1e6f604e6fce74fabf73af2e39a37979d0223
  md5: 75be1a943e0a7f99fcf118309092c635
  depends:
  - typing_extensions ==4.14.1 pyhe01879c_0
  license: PSF-2.0
  license_family: PSF
  size: 90486
  timestamp: 1751643513473
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.14.1-pyhe01879c_0.conda
  sha256: 4f52390e331ea8b9019b87effaebc4f80c6466d09f68453f52d5cdc2a3e1194f
  md5: e523f4f1e980ed7a4240d7e27e9ec81f
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  size: 51065
  timestamp: 1751643513473
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
  sha256: 3088d5d873411a56bf988eee774559335749aed6f6c28e07bf933256afb9eb6c
  md5: f6d7aa696c67756a650e91e15e88223c
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 15183
  timestamp: 1733331395943
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/linux-64/ukkonen-1.0.1-py312h68727a3_5.conda
  sha256: 9fb020083a7f4fee41f6ece0f4840f59739b3e249f157c8a407bb374ffb733b5
  md5: f9664ee31aed96c85b7319ab0a693341
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 13904
  timestamp: 1725784191021
- conda: https://conda.anaconda.org/conda-forge/osx-64/ukkonen-1.0.1-py312hc5c4d5f_5.conda
  sha256: f6433143294c1ca52410bf8bbca6029a04f2061588d32e6d2b67c7fd886bc4e0
  md5: f270aa502d8817e9cb3eb33541f78418
  depends:
  - __osx >=10.13
  - cffi
  - libcxx >=17
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 13031
  timestamp: 1725784199719
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/ukkonen-1.0.1-py312h6142ec9_5.conda
  sha256: 1e4452b4a12d8a69c237f14b876fbf0cdc456914170b49ba805779c749c31eca
  md5: 2b485a809d1572cbe7f0ad9ee107e4b0
  depends:
  - __osx >=11.0
  - cffi
  - libcxx >=17
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: MIT
  license_family: MIT
  size: 13605
  timestamp: 1725784243533
- conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py312h66e93f0_0.conda
  sha256: 638916105a836973593547ba5cf4891d1f2cb82d1cf14354fcef93fd5b941cdc
  md5: 617f5d608ff8c28ad546e5d9671cbb95
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 404401
  timestamp: 1736692621599
- conda: https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py312h01d7ebd_0.conda
  sha256: ac5cc7728c3052777aa2d54dde8735f677386b38e3a4c09a805120274a8b3475
  md5: 27740ecb2764b1cddbe1e7412ed16034
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 399510
  timestamp: 1736692713652
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/unicodedata2-16.0.0-py312hea69d52_0.conda
  sha256: c6ca9ea11eecc650df4bce4b3daa843821def6d753eeab6d81de35bb43f9d984
  md5: 9a835052506b91ea8f0d8e352cd12246
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: Apache-2.0
  license_family: Apache
  size: 409745
  timestamp: 1736692768349
- conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
  sha256: e0eb6c8daf892b3056f08416a96d68b0a358b7c46b99c8a50481b22631a4dfc0
  md5: e7cb0f5745e4c5035a460248334af7eb
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 23990
  timestamp: 1733323714454
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda
  sha256: 4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8
  md5: 436c165519e140cb08d246a4472a9d6a
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  size: 101735
  timestamp: 1750271478254
- conda: https://conda.anaconda.org/conda-forge/noarch/virtualenv-20.32.0-pyhd8ed1ab_0.conda
  sha256: 7a6eb58af8aa022202ca9f29aa6278f8718780a190de90280498ffd482f23e3e
  md5: 3d6c6f6498c5fb6587dc03ff9541feeb
  depends:
  - distlib >=0.3.7,<1
  - filelock >=3.12.2,<4
  - platformdirs >=3.9.1,<5
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 4135484
  timestamp: 1753096346652
- conda: https://conda.anaconda.org/conda-forge/linux-64/watchdog-6.0.0-py312h7900ff3_0.conda
  sha256: 2436c4736b8135801f6bfcd09c7283f2d700a66a90ebd14b666b996e33ef8c9a
  md5: 687b37d1325f228429409465e811c0bc
  depends:
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - pyyaml >=3.10
  license: Apache-2.0
  license_family: APACHE
  size: 140940
  timestamp: 1730493008472
- conda: https://conda.anaconda.org/conda-forge/osx-64/watchdog-6.0.0-py312h01d7ebd_0.conda
  sha256: 81ca10842962d6d8d18cb58f36f365e85a916fdf5fe7ac98351eafdfcd3c1030
  md5: 6bf329e9cdc3e6d65c0d11a43eca6e21
  depends:
  - __osx >=10.13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  - pyyaml >=3.10
  license: Apache-2.0
  license_family: APACHE
  size: 148305
  timestamp: 1730493092622
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/watchdog-6.0.0-py312hea69d52_0.conda
  sha256: f6c2eb941ffc25fc4fc637c71a5465678ed20e57b53698020a50dca86c584f04
  md5: ce2a02fd5a911d4eb963af9a84c00d2c
  depends:
  - __osx >=11.0
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  - pyyaml >=3.10
  license: Apache-2.0
  license_family: APACHE
  size: 149164
  timestamp: 1730493202256
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.24.0-h3e06ad9_0.conda
  sha256: ba673427dcd480cfa9bbc262fd04a9b1ad2ed59a159bd8f7e750d4c52282f34c
  md5: 0f2ca7906bf166247d1d760c3422cb8a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  size: 330474
  timestamp: 1751817998141
- conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
  sha256: f21e63e8f7346f9074fd00ca3b079bd3d2fa4d71f1f89d5b6934bf31446dc2a5
  md5: b68980f2495d096e71c7fd9d7ccf63e6
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 32581
  timestamp: 1733231433877
- conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
  sha256: 08315dc2e61766a39219b2d82685fc25a56b2817acf84d5b390176080eaacf99
  md5: b49f7b291e15494aafb0a7d74806f337
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 18431
  timestamp: 1733359823938
- conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
  sha256: 19ff205e138bb056a46f9e3839935a2e60bd1cf01c8241a5e172a422fed4f9c6
  md5: 2841eb5bfc75ce15e9a0054b98dcd64d
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 15496
  timestamp: 1733236131358
- conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
  sha256: 1dd84764424ffc82030c19ad70607e6f9e3b9cb8e633970766d697185652053e
  md5: 84f8f77f0a9c6ef401ee96611745da8f
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  size: 46718
  timestamp: 1733157432924
- conda: https://conda.anaconda.org/conda-forge/linux-64/wget-1.21.4-hda4d442_0.conda
  sha256: 70df4ac8cca488618458af4705706551cef7e402bac9c2c41dd17148f60cbd1f
  md5: 361e96b664eac64a33c20dfd11affbff
  depends:
  - libgcc-ng >=12
  - libidn2 >=2,<3.0a0
  - libunistring >=0,<1.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - openssl >=3.2.1,<4.0a0
  - zlib
  license: GPL-3.0-or-later
  license_family: GPL
  size: 770380
  timestamp: 1710770110704
- conda: https://conda.anaconda.org/conda-forge/osx-64/wget-1.21.4-hca547e6_0.conda
  sha256: 92e8e769dd3eb517b68d2e1ed81ac3a76c025ca54e55a0ad4523ef606c99c44a
  md5: 479910a14056bea9c36bbb867c3f1471
  depends:
  - libidn2 >=2,<3.0a0
  - libunistring >=0,<1.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - openssl >=3.2.1,<4.0a0
  - zlib
  license: GPL-3.0-or-later
  license_family: GPL
  size: 777628
  timestamp: 1710770496957
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/wget-1.21.4-he2df1f1_0.conda
  sha256: df0ef5aed0791ebcb27692bfdee0785ebcf02eb770479c53a3e4cdd775d18f1a
  md5: c56417aa2d4f73708bf453cfc295a25d
  depends:
  - libidn2 >=2,<3.0a0
  - libunistring >=0,<1.0a0
  - libzlib >=1.2.13,<2.0.0a0
  - openssl >=3.2.1,<4.0a0
  - zlib
  license: GPL-3.0-or-later
  license_family: GPL
  size: 777150
  timestamp: 1710770624608
- conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
  sha256: 7df3620c88343f2d960a58a81b79d4e4aa86ab870249e7165db7c3e2971a2664
  md5: 2f1f99b13b9d2a03570705030a0b3e7c
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  size: 889285
  timestamp: 1744291155057
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-h4f16b4b_2.conda
  sha256: ad8cab7e07e2af268449c2ce855cbb51f43f4664936eff679b1f3862e6e4b01d
  md5: fdc27cb255a7a2cc73b7919a968b48f0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 20772
  timestamp: 1750436796633
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
  sha256: c7b35db96f6e32a9e5346f97adc968ef2f33948e3d7084295baebc0e33abdd5b
  md5: eb44b3b6deb1cab08d72cb61686fe64c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.13
  - libxcb >=1.16,<2.0.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  license: MIT
  license_family: MIT
  size: 20296
  timestamp: 1726125844850
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
  sha256: 94b12ff8b30260d9de4fd7a28cca12e028e572cbc504fd42aa2646ec4a5bded7
  md5: a0901183f08b6c7107aab109733a3c91
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  license: MIT
  license_family: MIT
  size: 24551
  timestamp: 1718880534789
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
  sha256: 546e3ee01e95a4c884b6401284bb22da449a2f4daf508d038fdfa0712fe4cc69
  md5: ad748ccca349aec3e91743e08b5e2b50
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  size: 14314
  timestamp: 1718846569232
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
  sha256: 2d401dadc43855971ce008344a4b5bd804aca9487d8ebd83328592217daca3df
  md5: 0e0cbe0564d03a99afd5fd7b362feecd
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  size: 16978
  timestamp: 1718848865819
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
  sha256: 31d44f297ad87a1e6510895740325a635dd204556aa7e079194a0034cdd7e66a
  md5: 608e0ef8256b81d04456e8d211eee3e8
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  size: 51689
  timestamp: 1718844051451
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxau-1.0.12-h6e16a3a_0.conda
  sha256: b4d2225135aa44e551576c4f3cf999b3252da6ffe7b92f0ad45bb44b887976fc
  md5: 4cf40e60b444d56512a64f39d12c20bd
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 13290
  timestamp: 1734229077182
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxau-1.0.12-h5505292_0.conda
  sha256: f33e6f013fc36ebc200f09ddead83468544cb5c353a3b50499b07b8c34e28a8d
  md5: 50901e0764b7701d8ed7343496f4f301
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 13593
  timestamp: 1734229104321
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
  sha256: 753f73e990c33366a91fd42cc17a3d19bb9444b9ca5ff983605fa9e953baf57f
  md5: d3c295b50f092ab525ffe3c2aa4b7413
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  size: 13603
  timestamp: 1727884600744
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
  sha256: 43b9772fd6582bf401846642c4635c47a9b0e36ca08116b3ec3df36ab96e0ec0
  md5: b5fcc7172d22516e1f965490e65e33a4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  size: 13217
  timestamp: 1727891438799
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/osx-64/xorg-libxdmcp-1.1.5-h00291cd_0.conda
  sha256: bb4d1ef9cafef535494adf9296130b6193b3a44375883185b5167de03eb1ac7f
  md5: 9f438e1b6f4e73fd9e6d78bfe7c36743
  depends:
  - __osx >=10.13
  license: MIT
  license_family: MIT
  size: 18465
  timestamp: 1727794980957
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/xorg-libxdmcp-1.1.5-hd74edd7_0.conda
  sha256: 9939a166d780700d81023546759102b33fdc2c5f11ef09f5f66c77210fd334c8
  md5: 77c447f48cab5d3a15ac224edb86a968
  depends:
  - __osx >=11.0
  license: MIT
  license_family: MIT
  size: 18487
  timestamp: 1727795205022
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
  sha256: 1a724b47d98d7880f26da40e45f01728e7638e6ec69f35a3e11f92acd05f9e7a
  md5: 17dcc85db3c7886650b8908b183d6876
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  size: 47179
  timestamp: 1727799254088
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
  sha256: ac0f037e0791a620a69980914a77cb6bb40308e26db11698029d6708f5aa8e0d
  md5: 2de7f99d6581a4a7adbff607b5c278ca
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  size: 29599
  timestamp: 1727794874300
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
  sha256: 752fdaac5d58ed863bbf685bb6f98092fe1a488ea8ebb7ed7b606ccfce08637a
  md5: 7bbe9a0cc0df0ac5f5a8ad6d6a11af2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxi >=1.7.10,<2.0a0
  license: MIT
  license_family: MIT
  size: 32808
  timestamp: 1727964811275
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
  sha256: 8a4e2ee642f884e6b78c20c0892b85dd9b2a6e64a6044e903297e616be6ca35b
  md5: 5efa5fa6243a622445fdfd72aee15efa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  size: 17819
  timestamp: 1734214575628
- conda: https://conda.anaconda.org/conda-forge/noarch/xyzservices-2025.4.0-pyhd8ed1ab_0.conda
  sha256: ac6d4d4133b1e0f69075158cdf00fccad20e29fc6cc45faa480cec37a84af6ae
  md5: 5663fa346821cd06dc1ece2c2600be2c
  depends:
  - python >=3.8
  license: BSD-3-Clause
  license_family: BSD
  size: 49477
  timestamp: 1745598150265
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  size: 89141
  timestamp: 1641346969816
- conda: https://conda.anaconda.org/conda-forge/osx-64/yaml-0.2.5-h0d85af4_2.tar.bz2
  sha256: 5301417e2c8dea45b401ffee8df3957d2447d4ce80c83c5ff151fc6bfe1c4148
  md5: d7e08fcf8259d742156188e8762b4d20
  license: MIT
  license_family: MIT
  size: 84237
  timestamp: 1641347062780
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/yaml-0.2.5-h3422bc3_2.tar.bz2
  sha256: 93181a04ba8cfecfdfb162fc958436d868cc37db504c58078eab4c1a3e57fbb7
  md5: 4bb3f014845110883a3c5ee811fd84b4
  license: MIT
  license_family: MIT
  size: 88016
  timestamp: 1641347076660
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/osx-64/zeromq-4.3.5-h7130eaa_7.conda
  sha256: b932dce8c9de9a8ffbf0db0365d29677636e599f7763ca51e554c43a0c5f8389
  md5: 6a0a76cd2b3d575e1b7aaeb283b9c3ed
  depends:
  - __osx >=10.13
  - krb5 >=1.21.3,<1.22.0a0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  license: MPL-2.0
  license_family: MOZILLA
  size: 292112
  timestamp: 1731585246902
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zeromq-4.3.5-hc1bb282_7.conda
  sha256: 9e585569fe2e7d3bea71972cd4b9f06b1a7ab8fa7c5139f92a31cbceecf25a8a
  md5: f7e6b65943cb73bce0143737fded08f1
  depends:
  - __osx >=11.0
  - krb5 >=1.21.3,<1.22.0a0
  - libcxx >=18
  - libsodium >=1.0.20,<1.0.21.0a0
  license: MPL-2.0
  license_family: MOZILLA
  size: 281565
  timestamp: 1731585108039
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.23.0-pyhd8ed1ab_0.conda
  sha256: 7560d21e1b021fd40b65bfb72f67945a3fcb83d78ad7ccf37b8b3165ec3b68ad
  md5: df5e78d904988eb55042c0c97446079f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  size: 22963
  timestamp: 1749421737203
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  size: 92286
  timestamp: 1727963153079
- conda: https://conda.anaconda.org/conda-forge/osx-64/zlib-1.3.1-hd23fc13_2.conda
  sha256: 219edbdfe7f073564375819732cbf7cc0d7c7c18d3f546a09c2dfaf26e4d69f3
  md5: c989e0295dcbdc08106fe5d9e935f0b9
  depends:
  - __osx >=10.13
  - libzlib 1.3.1 hd23fc13_2
  license: Zlib
  license_family: Other
  size: 88544
  timestamp: 1727963189976
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zlib-1.3.1-h8359307_2.conda
  sha256: 58f8860756680a4831c1bf4f294e2354d187f2e999791d53b1941834c4b37430
  md5: e3170d898ca6cb48f1bb567afb92f775
  depends:
  - __osx >=11.0
  - libzlib 1.3.1 h8359307_2
  license: Zlib
  license_family: Other
  size: 77606
  timestamp: 1727963209370
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py312h66e93f0_2.conda
  sha256: ff62d2e1ed98a3ec18de7e5cf26c0634fd338cb87304cf03ad8cbafe6fe674ba
  md5: 630db208bc7bbb96725ce9832c7423bb
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 732224
  timestamp: 1745869780524
- conda: https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.23.0-py312h01d7ebd_2.conda
  sha256: 970db6b96b9ac7c1418b8743cf63c3ee6285ec7f56ffc94ac7850b4c2ebc3095
  md5: 64aea64b791ab756ef98c79f0e48fee5
  depends:
  - __osx >=10.13
  - cffi >=1.11
  - python >=3.12,<3.13.0a0
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 690063
  timestamp: 1745869852235
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstandard-0.23.0-py312hea69d52_2.conda
  sha256: c499a2639c2981ac2fd33bae2d86c15d896bc7524f1c5651a7d3b088263f7810
  md5: ba0eb639914e4033e090b46f53bec31c
  depends:
  - __osx >=11.0
  - cffi >=1.11
  - python >=3.12,<3.13.0a0
  - python >=3.12,<3.13.0a0 *_cpython
  - python_abi 3.12.* *_cp312
  license: BSD-3-Clause
  license_family: BSD
  size: 532173
  timestamp: 1745870087418
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 567578
  timestamp: 1742433379869
- conda: https://conda.anaconda.org/conda-forge/osx-64/zstd-1.5.7-h8210216_2.conda
  sha256: c171c43d0c47eed45085112cb00c8c7d4f0caa5a32d47f2daca727e45fb98dca
  md5: cd60a4a5a8d6a476b30d8aa4bb49251a
  depends:
  - __osx >=10.13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 485754
  timestamp: 1742433356230
- conda: https://conda.anaconda.org/conda-forge/osx-arm64/zstd-1.5.7-h6491c7d_2.conda
  sha256: 0d02046f57f7a1a3feae3e9d1aa2113788311f3cf37a3244c71e61a93177ba67
  md5: e6f69c7bcccdefa417f056fa593b40f0
  depends:
  - __osx >=11.0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  size: 399979
  timestamp: 1742433432699
