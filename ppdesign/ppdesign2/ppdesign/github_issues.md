# GitHub Issues for PPDesign gRNA Thermodynamic Enhancement

## Enhancement Issues to Create

### Issue 1: Expand Thermodynamic Parameters Database
**Title:** Add comprehensive RNA-DNA hybrid parameters for all sequence contexts
**Labels:** enhancement, thermodynamics
**Description:**
Current implementation uses basic nearest-neighbor parameters from <PERSON><PERSON><PERSON> et al. (1995). We should:
- Add parameters for wobble pairs (G-U)
- Include terminal mismatch parameters
- Add dangling end corrections
- Incorporate salt correction models from recent literature (Owczarzy 2008, 2014)
- Add temperature-dependent corrections

**Tasks:**
- [ ] Research latest thermodynamic parameter sets
- [ ] Implement wobble pair calculations
- [ ] Add terminal mismatch corrections
- [ ] Validate against experimental data

---

### Issue 2: Implement NUPACK-style Ensemble Prediction
**Title:** Add ensemble thermodynamic predictions for multi-state binding
**Labels:** enhancement, algorithm
**Description:**
Implement partition function calculations to predict:
- Multiple binding conformations
- Secondary structure competition
- Ensemble average properties
- Boltzmann-weighted binding probabilities

**References:**
- NUPACK algorithms (<PERSON><PERSON><PERSON> et al., 2011)
- ViennaRNA package methods

---

### Issue 3: Add Machine Learning-based Correction Factors
**Title:** Integrate ML models for thermodynamic prediction refinement
**Labels:** enhancement, machine-learning
**Description:**
Use ML to correct thermodynamic predictions based on:
- Sequence context beyond nearest-neighbors
- GC content and distribution
- Structural features
- Experimental binding data

**Suggested approaches:**
- Random Forest for ΔG correction
- Neural network for Tm prediction
- Gradient boosting for off-target scoring

---

### Issue 4: Implement Kinetic Model for Time-dependent Binding
**Title:** Add kinetic modeling for association/dissociation rates
**Labels:** enhancement, kinetics
**Description:**
Extend beyond equilibrium thermodynamics to include:
- kon and koff rate calculations
- Time-to-equilibrium predictions
- Temperature-dependent rate constants
- Competitive binding kinetics

**Mathematical framework:**
- Arrhenius equation for temperature dependence
- Eyring equation for transition state theory
- Diffusion-limited association rates

---

### Issue 5: Create Web-based Thermodynamic Visualization Tool
**Title:** Interactive web app for thermodynamic profile visualization
**Labels:** enhancement, visualization, web
**Description:**
Build Streamlit/Dash app for:
- Real-time thermodynamic calculations
- Interactive ΔG vs Tm plots
- Melting curve simulations
- Off-target heatmaps
- Downloadable reports

**Features:**
- Upload guide RNA sequences
- Adjust experimental conditions
- Compare multiple guides
- Export publication-ready figures

---

### Issue 6: Add Support for Modified Nucleotides
**Title:** Thermodynamic parameters for LNA, 2'-O-Me, and other modifications
**Labels:** enhancement, chemistry
**Description:**
Extend calculator to handle:
- Locked nucleic acids (LNA)
- 2'-O-methyl RNA
- Phosphorothioate backbones
- Other chemical modifications

**Impact on:**
- Binding affinity
- Specificity
- Nuclease resistance

---

### Issue 7: Implement Cofolding Analysis
**Title:** Add RNA-RNA cofolding predictions for guide-target complexes
**Labels:** enhancement, structure
**Description:**
Predict:
- Guide RNA secondary structures
- Target RNA structures
- Cofolded complex structures
- Structure-based accessibility scores

**Tools to integrate:**
- RNAcofold algorithms
- IntaRNA-style accessibility
- Local structure predictions

---

### Issue 8: Create Thermodynamic Database from Literature
**Title:** Build curated database of experimental gRNA thermodynamic data
**Labels:** data, database
**Description:**
Collect and organize:
- Published Tm measurements
- Binding affinity data (Kd, Ka)
- Off-target binding profiles
- Temperature-dependent studies

**Format:**
- JSON/SQLite database
- Standardized units
- Quality scores
- Reference tracking

---

### Issue 9: Optimize Calculations for Large-scale Screening
**Title:** Performance optimization for genome-wide gRNA thermodynamic analysis
**Labels:** performance, optimization
**Description:**
Current implementation needs optimization for:
- Parallel processing (multiprocessing/Ray)
- Vectorized numpy operations
- Cython/Numba acceleration
- GPU acceleration for matrix operations
- Caching of repeated calculations

**Benchmarks needed:**
- 1M guide screening
- Genome-wide off-target analysis
- Memory usage profiling

---

### Issue 10: Add Experimental Validation Module
**Title:** Module for comparing predictions with experimental results
**Labels:** validation, analysis
**Description:**
Create tools for:
- Importing qPCR melt curve data
- Comparing predicted vs measured Tm
- ROC curve analysis for off-target predictions
- Statistical significance testing
- Generating validation reports

**Input formats:**
- CSV/Excel from thermocyclers
- FASTA with measured values
- JSON from analysis pipelines

---

## Bug Fixes

### Issue 11: Fix Edge Cases in Mismatch Calculations
**Title:** Handle edge cases in mismatch penalty calculations
**Labels:** bug, thermodynamics
**Description:**
Current implementation may fail for:
- Sequences with N bases
- Gaps at sequence ends
- Multiple consecutive mismatches

---

### Issue 12: Correct Salt Correction Formula
**Title:** Update salt correction to use most recent models
**Labels:** bug, calculation
**Description:**
Current simplified salt correction should be replaced with:
- Owczarzy 2008 unified model
- Mg2+ competition effects
- Mixed monovalent/divalent corrections

---

## Documentation

### Issue 13: Create Thermodynamics Tutorial
**Title:** Comprehensive tutorial for thermodynamic analysis features
**Labels:** documentation
**Description:**
Write tutorial covering:
- Theory behind calculations
- Interpretation of results
- Best practices for different applications
- Troubleshooting guide
- Example workflows

---

### Issue 14: API Documentation for Thermodynamic Module
**Title:** Complete API docs for thermodynamics.py
**Labels:** documentation
**Description:**
Add:
- Detailed docstrings
- Parameter descriptions
- Return value specifications
- Usage examples
- Mathematical formulas in docs

---

## Testing

### Issue 15: Comprehensive Test Suite for Thermodynamics
**Title:** Add unit and integration tests for thermodynamic calculations
**Labels:** testing
**Description:**
Test coverage for:
- Known ΔG/Tm values from literature
- Edge cases
- Different experimental conditions
- Performance benchmarks
- Regression tests

**Test data needed:**
- Published experimental values
- Synthetic test cases
- Real-world guide RNA examples