# PPDesign - Probe and Primer Design Pipeline

[![Pi<PERSON>](https://img.shields.io/badge/Pixi-Enabled-blue)](https://pixi.sh)
[![Python](https://img.shields.io/badge/python-3.11%2B-blue)](https://www.python.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

PPDesign is a modern bioinformatics pipeline for designing oligonucleotide probes, primers, and CRISPR guide RNAs targeting specific taxonomic groups. It offers multiple approaches: gene-based design using codon alignments, direct nucleotide-based design using k-mer analysis, and CRISPR guide RNA design for conserved regions.

## 🧬 Key Features

- **Triple-mode operation**: Gene-based probe design, direct nucleotide probe design, and CRISPR guide RNA design
- **CRISPR guide RNA design**: SpCas9-compatible PAM recognition (NGG/NAG) for conserved region targeting
- **Database integration**: Direct access to NeLLi genome database with 700K+ bacterial/archaeal genomes
- **Advanced algorithms**: K-mer seeding, MSA-based conservation, and codon-aware alignment
- **Comprehensive filtering**: GC content, melting temperature, secondary structure checks
- **Degenerate nucleotide support**: IUPAC codes for capturing sequence variation
- **Parallel processing**: Multi-threaded design for large-scale analyses
- **Visualization**: Probe binding site mapping and ranking

## 🔬 Pipeline Architecture

```mermaid
graph TB
    %% Define styles
    classDef inputStyle fill:#e1f5e1,stroke:#4caf50,stroke-width:3px,color:#1b5e20
    classDef mode1Style fill:#e3f2fd,stroke:#2196f3,stroke-width:3px,color:#0d47a1
    classDef mode2Style fill:#fce4ec,stroke:#e91e63,stroke-width:3px,color:#880e4f
    classDef processStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100
    classDef outputStyle fill:#f3e5f5,stroke:#9c27b0,stroke-width:3px,color:#4a148c
    classDef dbStyle fill:#e0f2f1,stroke:#009688,stroke-width:3px,color:#004d40

    %% Input sources
    subgraph inputs["📥 Input Sources"]
        DB[(NeLLi Genome<br/>Database<br/>700K+ genomes)]:::dbStyle
        LOCAL[Local FASTA<br/>Files]:::inputStyle
        TAXA[Taxonomic<br/>Query]:::inputStyle
    end

    %% Mode 1: Gene-based
    subgraph mode1["🧬 Mode 1: Gene-Based Design"]
        DOWNLOAD[Download<br/>Genomes]:::mode1Style
        GENECALL[Gene Calling<br/>prodigal-gv]:::mode1Style
        ORTHO[Ortholog Detection<br/>ProteinOrtho]:::mode1Style
        ALIGN[Protein Alignment<br/>MAFFT]:::mode1Style
        CODON[Codon Alignment<br/>pal2nal]:::mode1Style
    end

    %% Mode 2: Direct nucleotide
    subgraph mode2["🔤 Mode 2: Direct Nucleotide Design"]
        KMER[K-mer Analysis<br/>Seed & Extend]:::mode2Style
        MSA[MSA Analysis<br/>MUSCLE]:::mode2Style
        MINIMAP[Minimap2 Alignment<br/>Contigs/Genomes]:::mode2Style
        CONSERVE[Conservation<br/>Detection]:::mode2Style
    end

    %% Shared processing
    subgraph process["⚙️ Probe Processing & Filtering"]
        THERMO[Thermodynamic<br/>Filtering<br/>GC%, Tm]:::processStyle
        STRUCT[Secondary Structure<br/>Check<br/>Hairpins, Dimers]:::processStyle
        DEGEN[Degenerate Base<br/>Generation<br/>IUPAC Codes]:::processStyle
        QUALITY[Quality Scoring<br/>& Ranking]:::processStyle
    end

    %% Outputs
    subgraph outputs["📊 Outputs"]
        PROBES[Selected<br/>Probes/Primers]:::outputStyle
        VIZ[Binding Site<br/>Visualization]:::outputStyle
        REPORT[Analysis<br/>Report]:::outputStyle
    end

    %% Connections
    DB --> |Query| TAXA
    TAXA --> DOWNLOAD
    LOCAL --> GENECALL
    DOWNLOAD --> GENECALL
    
    GENECALL --> |FAA| ORTHO
    ORTHO --> |Groups| ALIGN
    ALIGN --> |Aligned| CODON
    CODON --> THERMO
    
    LOCAL --> |Direct| KMER
    LOCAL --> |Direct| MSA
    LOCAL --> |Direct| MINIMAP
    KMER --> CONSERVE
    MSA --> CONSERVE
    MINIMAP --> CONSERVE
    CONSERVE --> THERMO
    
    THERMO --> STRUCT
    STRUCT --> DEGEN
    DEGEN --> QUALITY
    QUALITY --> PROBES
    QUALITY --> VIZ
    QUALITY --> REPORT
```

## 🚀 Installation

### Using Pixi (Recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/ppdesign.git
cd ppdesign

# Install with pixi
pixi install

# Activate the environment
pixi shell
```

### Using pip

```bash
# Clone the repository
git clone https://github.com/yourusername/ppdesign.git
cd ppdesign

# Install in development mode
pip install -e ".[dev]"
```

## 🔧 Troubleshooting

### Common Issues and Solutions

1. **MAXGROUPS Error with Pixi**
   If you encounter `NameError: name 'MAXGROUPS' is not defined`:
   ```bash
   # Remove corrupted environment and reinstall
   rm -rf .pixi/envs/default && pixi install
   
   # Alternative: Run directly with Python
   python -m src.ppdesign.probedesign_grna [options]
   ```

2. **Results Directory Structure**
   - Results are saved in `results/<output_dir>`, not `results/results/<output_dir>`
   - Check the actual path in the output message

3. **Memory Issues with Large Datasets**
   - Reduce `--threads` parameter
   - Use `--limit` to process fewer sequences
   - Consider using `--cluster-threshold` to reduce redundancy

4. **Missing gRNA IDs in Alignment Viewer**
   - Ensure the gRNA ID exists in your results (check guide_rnas.csv)
   - Use the correct results directory path

## 📖 Usage

### Mode 1: Gene-Based Design (for bacteria and viruses)

This mode performs gene calling, finds orthologous groups, and designs probes from codon alignments.

#### Database Query Mode
```bash
# Design probes for bacterial genus Citrobacter
ppdesign-unified \
  --db-query \
  --taxonomy "g__Citrobacter" \
  --tax-level species \
  --conservation 0.3 \
  --threads 8 \
  --limit 10 \
  -o citrobacter_probes

# Design probes for a specific bacterial species
ppdesign-unified \
  --db-query \
  --taxonomy "s__Escherichia coli" \
  --tax-level strain \
  --conservation 0.8 \
  --threads 8 \
  --limit 50 \
  -o ecoli_probes
```

#### Local File Mode
```bash
# Use your own genome files
ppdesign-unified \
  --fasta-dir path/to/genomes/ \
  --conservation 0.3 \
  --threads 8 \
  -o my_probes
```

### Mode 2: Direct Nucleotide Design (no gene calling)

This mode finds conserved regions directly from nucleotide sequences using k-mer or MSA approaches.

#### K-mer Based Method (recommended for longer sequences)
```bash
# Design 20bp probes with specific thermodynamic properties
ppdesign-nucleotide \
  --fasta-dir path/to/sequences/ \
  --method kmer \
  --kmer-size 20 \
  --conservation 0.8 \
  --gc-min 40 --gc-max 60 \
  --tm-min 55 --tm-max 65 \
  --min-length 18 --max-length 25 \
  --threads 8 \
  -o nucleotide_probes
```

#### MSA Based Method (for short, highly similar sequences like amplicons)
```bash
# Note: Uses MUSCLE for alignment, best for sequences <1kb with high similarity
ppdesign-nucleotide \
  --fasta-dir path/to/amplicons/ \
  --method msa \
  --conservation 0.9 \
  --window-size 30 \
  --step-size 5 \
  --threads 8 \
  -o amplicon_probes
```

#### Minimap2 Based Method (for contigs and genomes)
```bash
# Note: Uses minimap2 for fast alignment of longer sequences (genomes, contigs)
# Ideal for finding conserved regions across divergent sequences
ppdesign-nucleotide \
  --fasta-dir path/to/genomes/ \
  --method minimap2 \
  --conservation 0.8 \
  --gc-min 45 --gc-max 60 \
  --tm-min 55 --tm-max 65 \
  --min-length 20 --max-length 25 \
  --threads 8 \
  -o genome_probes
```

### Mode 3: CRISPR Guide RNA Design

This mode identifies SpCas9-compatible guide RNAs in conserved regions across multiple sequences.

#### Quick Start Example

```bash
# Simple design with default parameters
pixi run ppdesign-grna \
  --fasta-input tests/test.fna \
  --output-dir quick_test \
  --conservation 0.5

# Check results
cat results/quick_test/summary.txt
```

#### Non-Degenerate Mode with Multiple Guide Generation (NEW!)

The `--no-degenerate` flag enables exact sequence mode, which:
- Uses only exact 20bp sequences (no IUPAC degenerate codes)
- Prioritizes 100% coverage of required targets
- Optimizes guide selection using greedy set cover algorithm
- Ideal for applications requiring precise, non-degenerate oligonucleotides

**Key Parameters for Multiple Guide Generation:**
- `--min-guides`: Target number of guides to generate (default: 50, returns all available if fewer exist)
- `--max-guides`: Maximum limit on guide count (default: no limit)
- `--min-additional-coverage`: Coverage threshold for adding guides (default: 0.05 = 5%)
- `--no-degenerate`: Ensures all guides are exact sequences without IUPAC codes

**Graceful Handling:** If fewer guides than requested are available, the tool:
1. Returns all available guides without error
2. Displays a helpful warning with suggestions
3. Recommends trying degenerate mode for more options

```bash
# Non-degenerate mode with required targets (recommended)
./ppdesign-grna \
  --fasta-dir sequences/ \
  --output-dir results/ \
  --no-degenerate \
  --required-targets seq1 seq2 seq3 \
  --conservation 0.3

# Or use a file with required target IDs
./ppdesign-grna \
  --fasta-dir sequences/ \
  --output-dir results/ \
  --no-degenerate \
  --required-targets-file critical_targets.txt \
  --conservation 0.3

# Real example with test data (tested and verified):
# 1. Create a file with critical target IDs
echo "cluster136|LANL011_sample3|m64044_240111_005933/119735720/ccs" > critical_targets.txt
echo "cluster136|LANL012_sample7|m64044_240112_010035/55312492/ccs" >> critical_targets.txt

# 2. Run non-degenerate mode
./ppdesign-grna \
  --fasta-dir tests/ \
  --output-dir test_nondegen_output \
  --no-degenerate \
  --required-targets-file critical_targets.txt \
  --conservation 0.3

# Output: 2 guides covering 96.2% of sequences (25/26), with 100% coverage of required targets
# Guide 1: ATTGGATTTCTCTAAGTCTG (covers both required + 18 additional sequences, 76.9% conservation)
# Guide 2: TTTGCAAGCCTTACAGACAT (covers 8 additional sequences, 30.8% conservation)
```

#### Perfect Coverage Mode (NEW!)

The `--perfect-coverage` flag enables a sophisticated guide selection algorithm that ensures each target sequence has at least N perfect gRNA matches. This mode uses a greedy set cover optimization to guarantee comprehensive coverage while minimizing the total number of guides needed.

**Key Features:**
- Ensures minimum coverage requirements per target sequence
- Uses greedy set cover algorithm for optimal guide selection
- Balances between coverage completeness and total guide count
- Ideal for applications requiring guaranteed editing of all targets

**Parameters:**
- `--perfect-coverage`: Enable perfect coverage mode
- `--min-coverage-per-target`: Minimum number of perfect gRNA matches required per target (default: 3)
- `--max-total-grnas`: Maximum total number of gRNAs to select (default: 20)
- `--conservation`: Minimum conservation threshold (guides below this are filtered out, even in perfect coverage mode)

**How It Works:**
1. Identifies ALL possible gRNAs in each target sequence
2. Groups gRNAs by exact sequence match across targets
3. Applies greedy set cover algorithm to select minimal guide set
4. Ensures each target has at least min_coverage_per_target guides
5. Stops when coverage requirements met or max_total_grnas reached

```bash
# Basic perfect coverage mode (works with both files and directories)
pixi run ppdesign-grna \
  --fasta-input tests/test.fna \
  --output-dir results/perfect_coverage \
  --perfect-coverage \
  --min-coverage-per-target 2 \
  --max-total-grnas 20 \
  --conservation 0.3

# Perfect coverage with higher requirements
pixi run ppdesign-grna \
  --fasta-input sequences/ \
  --output-dir results/high_coverage \
  --perfect-coverage \
  --min-coverage-per-target 5 \
  --max-total-grnas 30 \
  --conservation 0.2

# Combined with non-degenerate mode for exact sequences
pixi run ppdesign-grna \
  --fasta-input tests/test.fna \
  --output-dir results/perfect_exact \
  --perfect-coverage \
  --no-degenerate \
  --min-coverage-per-target 3 \
  --max-total-grnas 25 \
  --conservation 0.3
```

**Output Files (Perfect Coverage Mode):**
- `guide_rnas.csv`: Selected guides with coverage statistics
- `coverage_matrix.tsv`: Binary matrix showing which guides cover which targets
- `target_coverage_details.tsv`: Per-target coverage analysis
- `summary.txt`: Includes perfect coverage statistics

**When to Use Perfect Coverage Mode:**
- Multiplexed CRISPR screening requiring redundancy
- Applications where every target must be editable
- Experiments needing statistical robustness through replication
- Cases where guide RNA delivery is not limiting

**Important Notes:**
- The `--conservation` parameter still applies in perfect coverage mode - guides below this threshold won't be selected
- Set conservation lower (e.g., 0.3) if you need guides that may only match a few targets
- The tool now accepts both single FASTA files and directories with `--fasta-input`

**Algorithm Details:**
The greedy set cover algorithm prioritizes guides that:
1. Meet the minimum conservation threshold
2. Cover the most under-covered targets
3. Provide maximum new coverage
4. Have high quality scores

This ensures efficient coverage with minimal redundancy while meeting strict coverage requirements.

```

#### gRNA Alignment Viewer (NEW!)

Visualize how specific guide RNAs align to your target sequences with a rich terminal interface:

```bash
# View alignments for a specific gRNA (e.g., gRNA_0008)
# Note: Results are saved in results/<output_dir> structure
pixi run grna-align gRNA_0008 \
  --results-dir results/my_results \
  --fasta-input tests/test.fna

# Customize display options
pixi run grna-align gRNA_0001 \
  --results-dir results/my_results \
  --fasta-input tests/test.fna \
  --max-targets 30  # Show more target alignments
```

**Features:**
- Color-coded alignment display (green = match, red = mismatch)
- Perfect matches shown first
- Position and strand information
- Summary statistics for match quality
- Handles both forward and reverse strand matches

**Example Output:**
```
╭─────────────── gRNA Information ──────────────╮
│ Guide RNA: gRNA_0008                          │
│ Sequence: TTATGGTACCAGAGAAAGAG                │
│ PAM: NGG | Conservation: 15.38%               │
╰────────────────────────────────────────────────╯

Target Sequence Alignments (showing top 4)
┏━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Target ID            ┃ Alignment               ┃
┡━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ LANL012_sample7      │ Pos 1019 [+] Perfect    │
│ LANL012_sample6      │ Pos 1498 [+] Perfect    │
│ LANL011_sample6      │ Pos   41 [+] Perfect    │
│ LANL011_sample7      │ Pos  120 [+] Perfect    │
└──────────────────────┴─────────────────────────┘
```

#### Complete Workflow Example: Design → Analyze → Visualize

Here's a complete tested workflow from gRNA design to validation:

```bash
# 1. Design gRNAs with perfect coverage mode
pixi run ppdesign-grna \
  --fasta-input tests/test.fna \
  --output-dir my_results \
  --perfect-coverage \
  --min-coverage-per-target 2 \
  --conservation 0.3

# 2. Check what gRNAs were generated
head results/my_results/guide_rnas.csv

# 3. View specific gRNA alignment (e.g., gRNA_0008)
pixi run grna-align gRNA_0008 \
  --results-dir results/my_results \
  --fasta-input tests/test.fna

# 4. Check coverage statistics to verify all targets are covered
cat results/my_results/target_coverage_details.tsv | head -10

# 5. View the summary for overall statistics
cat results/my_results/summary.txt

# 6. Optional: Check coverage matrix to see which guides cover which targets
head results/my_results/coverage_matrix.tsv
```

**Expected Output:**
- 10 guide RNAs selected
- 100% target coverage (26/26 sequences)
- Each target has at least 2 perfect matches
- Average 2.3 guides per target

#### Complete Guide RNA Design Workflow

Here's a comprehensive example workflow from design to analysis:

```bash
# Step 1: Design guide RNAs with all available parameters
# Combines conservation, GC content filtering, sequence clustering, and target prioritization
# NEW: --min-gc/--max-gc for GC content filtering, --cluster-threshold for sequence clustering!
pixi run ppdesign-grna \
  --fasta-input tests/test.fna \
  --output-dir my_output \
  --conservation 0.7 \
  --no-degenerate \
  --min-guides 5 \
  --max-guides 20 \
  --pam-type spCas9 \
  --min-gc 40 --max-gc 60 \
  --cluster-threshold 0.85 \
  --min-additional-coverage 0.01 \
  --required-targets "NC_003630_1|Pepper_mild_mottle_virus"

# Step 2: Validate guide RNAs against target sequences
# Basic validation with alignment statistics
pixi run validate-grna \
  --guide-csv results/my_output/guide_rnas.csv \
  --guide-fasta results/my_output/guide_rnas.fasta \
  --target-fasta tests/test.fna

# Step 3: Thermodynamic analysis for binding stability
# Uses CRISPR-appropriate buffer conditions (150 mM NaCl, 10 mM MgCl2)
pixi run validate-grna-thermo \
  --guide-csv results/my_output/guide_rnas.csv \
  --guide-fasta results/my_output/guide_rnas.fasta \
  --target-fasta tests/test.fna

# Step 4: Interactive visualization and analysis
# Option A: Local access only
pixi run jupyter-local notebooks/grna_visualization.ipynb

# Option B: Tailscale network access (for team collaboration)
pixi run jupyter-tailscale
# Team members can then access via http://your-machine-name:8888
```

#### Additional Design Examples

```bash
# Design guide RNAs with GC content filtering
# Ensures guides have optimal GC content for stability and activity
pixi run ppdesign-grna \
  --fasta-input sequences.fna \
  --output-dir guide_rnas/ \
  --pam-type spCas9 \
  --conservation 0.9 \
  --max-degenerate 1 \
  --min-gc 40 --max-gc 60

# Design guide RNAs with sequence clustering
# Reduces redundancy by clustering similar sequences before analysis
pixi run ppdesign-grna \
  --fasta-input sequences.fna \
  --output-dir guide_rnas/ \
  --conservation 0.7 \
  --cluster-threshold 0.85 \
  --mismatches 2

# Combined parameters for comprehensive filtering
# Uses all available filters for optimal guide selection
pixi run ppdesign-grna \
  --fasta-input sequences.fna \
  --output-dir optimal_guides/ \
  --conservation 0.8 \
  --no-degenerate \
  --min-gc 45 --max-gc 55 \
  --cluster-threshold 0.9 \
  --perfect-coverage \
  --min-coverage-per-target 2
```

#### Validate Guide RNAs for cDNA Applications

For single-stranded cDNA targets, use the specialized validation tool that analyzes strand bias:

```bash
# Basic cDNA validation with strand analysis
pixi run validate-grna-cdna \
  --guide-csv results/guide_rnas/guide_rnas.csv \
  --guide-fasta results/guide_rnas/guide_rnas.fasta \
  --target-fasta input_sequences.fna \
  --output cdna_validation.json

# Require perfect matches for critical sequences
pixi run validate-grna-cdna \
  --guide-csv results/guide_rnas/guide_rnas.csv \
  --guide-fasta results/guide_rnas/guide_rnas.fasta \
  --target-fasta input_sequences.fna \
  --required-targets seq1 seq2 seq3 \
  --output cdna_validation.json

# Load required targets from file (one ID per line)
pixi run validate-grna-cdna \
  --guide-csv results/guide_rnas/guide_rnas.csv \
  --guide-fasta results/guide_rnas/guide_rnas.fasta \
  --target-fasta input_sequences.fna \
  --required-targets-file critical_sequences.txt \
  --output cdna_validation.json

# The validation provides:
# - Forward (mRNA-sense) vs reverse (antisense) hit counts
# - Strand bias detection for cDNA applications
# - Perfect match tracking for all sequences
# - Required target coverage analysis
# - Conservation metrics across target sequences
# - Identification of guides suitable for single-stranded targets
```

### Terminal MSA Viewer for Guide RNA Validation

PPDesign includes a comprehensive terminal-based MSA (Multiple Sequence Alignment) viewer for validating and visualizing guide RNA matches. This tool provides colorful, interactive displays directly in your terminal.

#### Available Visualization Commands

##### 1. Basic Alignment View
View guide RNA alignments with automatic reverse complement handling:
```bash
# View top 5 gRNAs with 10 targets each
pixi run msa-view --num 5 --targets 10

# View specific number of alignments
pixi run msa-view --num 1 --targets 20
```

**Features:**
- Automatically reverse complements sequences for consistent forward orientation
- Shows conservation statistics (20/26 sequences = 76.92%)
- Color-coded highlighting: gRNA (red), PAM (cyan), flanking (gray)
- Displays actual vs requested target counts

##### 2. Detailed Alignment Analysis
Get precise alignment details for a specific gRNA:
```bash
# Show detailed alignment for gRNA_0001
pixi run msa-align --id gRNA_0001 --max 20

# Analyze specific gRNA with limited targets
pixi run msa-align --id gRNA_0002 --max 10
```

**Output includes:**
- Reference sequence with position markers
- All matching sequences aligned in forward orientation
- Perfect match validation
- Exact positions in target sequences

##### 3. Mismatch Visualization
View ALL sequences including those without perfect matches:
```bash
# Show sequences with up to 2 mismatches
pixi run msa-mismatch --id gRNA_0001 --mismatches 2

# Include sequences with more variations
pixi run msa-mismatch --id gRNA_0001 --mismatches 10
```

**Color coding:**
- 🟩 Green: Matching nucleotides
- 🔴 Red: Mismatched nucleotides  
- 🔵 Cyan: PAM sites

**Example output:**
```
Perfect matches: 20 sequences
Near matches (1-2 mismatches): 0 sequences
No matches: 6 sequences
Total: 26 sequences
```

##### 4. Statistical Overview
Get comprehensive statistics about your gRNA results:
```bash
pixi run msa-stats
```

**Shows:**
- Total gRNAs found
- Conservation distribution
- PAM type breakdown (NGG vs NAG)
- Quality score statistics

##### 5. Compare Multiple gRNAs
Side-by-side comparison of multiple guide RNAs:
```bash
# Compare specific gRNAs
pixi run msa-compare --ids "gRNA_0001,gRNA_0002,gRNA_0003"
```

##### 6. Consensus View
Quick overview of top performing gRNAs:
```bash
# View consensus for top 3 gRNAs
pixi run msa-consensus --num 3
```

#### Complete Workflow Example

```bash
# Step 1: Design guide RNAs with non-degenerate mode
pixi run ppdesign-grna \
  --fasta-dir sequences/ \
  --output-dir results/my_grnas \
  --conservation 0.5 \
  --no-degenerate \
  --min-guides 50

# Step 2: View statistical overview
pixi run msa-stats --csv results/my_grnas/guide_rnas.csv

# Step 3: Examine top performers
pixi run msa-view --num 5 --targets 15 \
  --csv results/my_grnas/guide_rnas.csv

# Step 4: Detailed analysis of best candidate
pixi run msa-align --id gRNA_0001 --max 20 \
  --csv results/my_grnas/guide_rnas.csv

# Step 5: Check for near-matches in all sequences
pixi run msa-mismatch --id gRNA_0001 --mismatches 3 \
  --csv results/my_grnas/guide_rnas.csv

# Step 6: Compare top candidates
pixi run msa-compare --ids "gRNA_0001,gRNA_0002,gRNA_0003" \
  --csv results/my_grnas/guide_rnas.csv
```

#### Understanding the Output

**Conservation Percentages:**
- `76.92% (20/26 sequences)` means the gRNA was found in 20 out of 26 total sequences
- Higher percentages indicate broader targeting across your sequence set

**Target Counts:**
- `Total Targets: 20` - Number of sequences where the gRNA matches
- `Showing: 18` - Number being displayed (based on your --targets parameter)

**Strand Information:**
- `+` indicates forward strand match
- `-` indicates reverse strand match (displayed in forward orientation)

#### Tips for Effective Use

1. **Start with statistics**: Use `msa-stats` to get an overview
2. **Check conservation**: Higher conservation means broader targeting
3. **Validate with mismatches**: Use `msa-mismatch` to understand why some sequences don't match
4. **Compare candidates**: Use `msa-compare` to evaluate multiple options
5. **Verify specificity**: Use `msa-align` for detailed position-by-position analysis

### Post-Processing Tools

#### Oligonucleotide Selection and Filtering
```bash
ppdesign-select \
  -i results/my_probes/codon_alignments \
  --gc-range 45 55 \
  --tm-range 58 62 \
  --length 20 25 \
  --window-size 25 \
  --step 5 \
  --threshold 0.8 \
  --threads 8
```

#### Probe Ranking and Visualization
```bash
ppdesign-rank \
  -o results/my_probes/selected_oligos.csv \
  -g reference_genome.fna \
  -r results/my_probes/rankings
```

## 📊 Output Structure

All results are organized in the `results/` directory:

```
results/
└── your_run_name/
    ├── data/
    │   ├── fna/              # Downloaded/input genomes
    │   ├── faa/              # Protein sequences
    │   └── fnn/              # Gene nucleotide sequences
    ├── proteinortho/         # Orthologous groups
    ├── alignments/           # MAFFT alignments
    ├── codon_alignments/     # Codon alignments
    ├── selected_oligos.csv   # Final probe designs
    └── conserved_regions.txt # Detailed conservation info
```

## 🔧 Advanced Parameters

### Conservation Levels
- `0.3` (30%): Low conservation, more degenerate probes
- `0.5` (50%): Moderate conservation, balanced specificity
- `0.8` (80%): High conservation, specific probes

### Thermodynamic Parameters
- **GC Content**: 40-60% (optimal for PCR)
- **Melting Temperature**: 55-65°C (standard PCR conditions)
- **Probe Length**: 18-25bp (optimal for specificity)

### Secondary Structure Checks
- **Hairpin ΔG**: > -3.0 kcal/mol
- **Self-dimer ΔG**: > -6.0 kcal/mol

## 🧪 Examples

### Example 1: Bacterial Genus Probe Design
```bash
# Design probes for Salmonella genus
ppdesign-unified \
  --db-query \
  --taxonomy "g__Salmonella" \
  --tax-level species \
  --conservation 0.5 \
  --threads 16 \
  --limit 20 \
  -o salmonella_probes
```

### Example 2: Viral Sequence Probe Design
```bash
# Design probes from viral amplicon sequences
ppdesign-nucleotide \
  --fasta-dir viral_amplicons/ \
  --method kmer \
  --kmer-size 20 \
  --conservation 0.7 \
  --gc-min 35 --gc-max 65 \
  --tm-min 50 --tm-max 70 \
  --threads 8 \
  -o viral_probes
```

### Example 3: Genome-Based Probe Design with Minimap2
```bash
# Design probes from bacterial genome assemblies using minimap2
ppdesign-nucleotide \
  --fasta-dir bacterial_genomes/ \
  --method minimap2 \
  --conservation 0.7 \
  --gc-min 50 --gc-max 65 \
  --tm-min 58 --tm-max 62 \
  --min-length 22 --max-length 25 \
  --threads 16 \
  -o genome_conserved_probes
```

### Example 4: High-Specificity Probe Design
```bash
# Design highly specific probes with strict filtering
ppdesign-unified \
  --db-query \
  --taxonomy "s__Mycobacterium tuberculosis" \
  --conservation 0.9 \
  --threads 8 \
  --limit 30 \
  -o mtb_specific_probes

# Apply strict filtering
ppdesign-select \
  -i results/mtb_specific_probes/codon_alignments \
  --gc-range 50 60 \
  --tm-range 60 65 \
  --length 22 25 \
  --threshold 0.95 \
  --threads 8
```

### Example 5: CRISPR Guide RNA Design
```bash
# Design guide RNAs for bacterial genomes
ppdesign-grna \
  --fasta-dir bacterial_genomes/ \
  --output-dir crispr_guides/ \
  --conservation 0.8 \
  --max-degenerate 2 \
  --min-gc 45 --max-gc 55 \
  --output-format all \
  --threads 16

# The output includes:
# - guide_rnas.csv: All guide RNAs with scores
# - guide_rnas.fasta: Guide sequences in FASTA format
# - guide_rnas.tsv: Tab-separated format for analysis
# - summary.txt: Statistics and top candidates
```

## 🛠️ Development

### Running Tests
```bash
pixi run test
```

### Code Quality
```bash
pixi run lint      # Run linters
pixi run format    # Format code
pixi run type-check # Type checking
```

### Building Documentation
```bash
pixi run docs       # Serve locally
pixi run build-docs # Build static
```

## 📁 Project Structure

```
ppdesign/
├── src/ppdesign/          # Source code
│   ├── probedesign_unified.py      # Mode 1: Gene-based
│   ├── probedesign_nucleotide.py   # Mode 2: Direct nucleotide
│   ├── probedesign_grna.py         # Mode 3: CRISPR guide RNA
│   ├── guide_rna_finder.py         # Guide RNA core algorithms
│   ├── conserved_finder.py         # Conservation algorithms
│   ├── genome_database.py          # Database interface
│   ├── probedesign_seqselection.py # Oligo filtering
│   ├── probedesign_rank.py         # Ranking & visualization
│   └── kmer_finder.py              # K-mer utilities
├── tests/                 # Test suite
├── docs/                  # Documentation
├── examples/              # Example data
├── resources/             # Resources
├── results/              # Output directory (gitignored)
├── pixi.toml             # Environment config
├── pyproject.toml        # Package config
└── README.md             # This file
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📚 Citation

If you use PPDesign in your research, please cite:

```bibtex
@software{ppdesign2024,
  title = {PPDesign: A Modern Pipeline for Oligonucleotide Probe and Primer Design},
  year = {2024},
  url = {https://github.com/yourusername/ppdesign}
}
```