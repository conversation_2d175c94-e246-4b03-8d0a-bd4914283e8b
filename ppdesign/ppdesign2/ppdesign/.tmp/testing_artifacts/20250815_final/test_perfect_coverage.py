#!/usr/bin/env python3
"""
Comprehensive test script for CRISPR guide RNA perfect coverage mode.

This script tests the --perfect-coverage feature implementation in the
ppdesign-grna command, ensuring proper functionality and edge case handling.
"""

import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Any
import pandas as pd


class PerfectCoverageTestSuite:
    """Test suite for perfect coverage mode functionality."""

    def __init__(self):
        self.base_dir = Path("/home/<USER>/dev/ppdesign")
        self.results_dir = self.base_dir / "results" / "perfect_coverage_test"
        self.test_results = []

        # Ensure results directory exists
        self.results_dir.mkdir(parents=True, exist_ok=True)

    def run_ppdesign_command(self, test_name: str, **kwargs) -> Dict[str, Any]:
        """Run ppdesign-grna command with given parameters."""

        # Default parameters
        default_params = {
            "output_dir": self.results_dir / test_name,
            "perfect_coverage": True,
            "min_coverage_per_target": 3,
            "max_total_grnas": 20,
            "conservation": 0.3,
        }

        # Update with provided parameters
        default_params.update(kwargs)

        # Build command
        cmd = ["pixi", "run", "ppdesign-grna"]

        for key, value in default_params.items():
            if key == "perfect_coverage" and value:
                cmd.append("--perfect-coverage")
            elif key == "fasta_dir":
                cmd.extend(["--fasta-dir", str(value)])
            elif key == "output_dir":
                cmd.extend(["--output-dir", str(value)])
            elif value is not None:
                cmd.extend([f"--{key.replace('_', '-')}", str(value)])

        print(f"\nRunning test: {test_name}")
        print(f"Command: {' '.join(cmd)}")

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=self.base_dir,
            )

            return {
                "test_name": test_name,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "success": result.returncode == 0,
                "output_dir": default_params["output_dir"],
            }

        except subprocess.TimeoutExpired:
            return {
                "test_name": test_name,
                "returncode": -1,
                "stdout": "",
                "stderr": "Command timed out after 300 seconds",
                "success": False,
                "output_dir": default_params["output_dir"],
            }

    def verify_coverage_requirements(
        self, output_dir: Path, min_coverage: int
    ) -> Dict[str, Any]:
        """Verify that coverage requirements are met."""

        coverage_stats = {
            "sequences": {},
            "total_guides": 0,
            "meets_requirements": True,
        }

        # Look for guide RNA output files
        grna_files = list(output_dir.glob("*guides*.csv"))
        if not grna_files:
            grna_files = list(output_dir.glob("*.csv"))

        if not grna_files:
            return {"error": "No gRNA output files found", "meets_requirements": False}

        try:
            # Read the main results file
            df = pd.read_csv(grna_files[0])
            coverage_stats["total_guides"] = len(df)

            # Count guides per sequence
            if "target_sequences" in df.columns:
                for _, row in df.iterrows():
                    targets = (
                        row["target_sequences"].split(",")
                        if pd.notna(row["target_sequences"])
                        else []
                    )
                    for target in targets:
                        target = target.strip()
                        if target:
                            coverage_stats["sequences"][target] = (
                                coverage_stats["sequences"].get(target, 0) + 1
                            )

            # Check if requirements are met
            for seq_id, count in coverage_stats["sequences"].items():
                if count < min_coverage:
                    coverage_stats["meets_requirements"] = False
                    coverage_stats[f"{seq_id}_shortfall"] = min_coverage - count

        except Exception as e:
            coverage_stats["error"] = str(e)
            coverage_stats["meets_requirements"] = False

        return coverage_stats

    def create_test_sequences(self, temp_dir: Path, sequences: Dict[str, str]):
        """Create FASTA files for testing."""

        fasta_dir = temp_dir / "fasta"
        fasta_dir.mkdir(exist_ok=True)

        fasta_file = fasta_dir / "test_sequences.fna"

        with open(fasta_file, "w") as f:
            for seq_id, sequence in sequences.items():
                f.write(f">{seq_id}\n{sequence}\n")

        return fasta_dir

    def test_basic_functionality(self):
        """Test basic perfect coverage functionality with existing test data."""

        result = self.run_ppdesign_command(
            "basic_test",
            fasta_dir=self.base_dir / "test_fasta",
            min_coverage_per_target=3,
            max_total_grnas=20,
            conservation=0.3,
        )

        # Verify coverage if successful
        if result["success"]:
            coverage_stats = self.verify_coverage_requirements(
                Path(result["output_dir"]), min_coverage=3
            )
            result["coverage_verification"] = coverage_stats

        self.test_results.append(result)
        return result

    def test_high_coverage_requirements(self):
        """Test with high coverage requirements."""

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create sequences with good conservation
            sequences = {
                "seq1": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
                "seq2": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
                "seq3": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
            }

            fasta_dir = self.create_test_sequences(temp_path, sequences)

            result = self.run_ppdesign_command(
                "high_coverage_test",
                fasta_dir=fasta_dir,
                min_coverage_per_target=5,
                max_total_grnas=30,
                conservation=0.8,
            )

            if result["success"]:
                coverage_stats = self.verify_coverage_requirements(
                    Path(result["output_dir"]), min_coverage=5
                )
                result["coverage_verification"] = coverage_stats

            self.test_results.append(result)
            return result

    def test_low_conservation_threshold(self):
        """Test with very low conservation threshold."""

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create sequences with low conservation
            sequences = {
                "seq1": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
                "seq2": "GTCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCA",
                "seq3": "CTCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATGT",
            }

            fasta_dir = self.create_test_sequences(temp_path, sequences)

            result = self.run_ppdesign_command(
                "low_conservation_test",
                fasta_dir=fasta_dir,
                min_coverage_per_target=2,
                max_total_grnas=15,
                conservation=0.1,
            )

            if result["success"]:
                coverage_stats = self.verify_coverage_requirements(
                    Path(result["output_dir"]), min_coverage=2
                )
                result["coverage_verification"] = coverage_stats

            self.test_results.append(result)
            return result

    def test_edge_case_no_common_grnas(self):
        """Test edge case with sequences that have no common gRNAs."""

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create completely different sequences
            sequences = {
                "seq1": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
                "seq2": "TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT",
                "seq3": "CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC",
            }

            fasta_dir = self.create_test_sequences(temp_path, sequences)

            result = self.run_ppdesign_command(
                "no_common_grnas_test",
                fasta_dir=fasta_dir,
                min_coverage_per_target=1,
                max_total_grnas=10,
                conservation=0.1,
            )

            # This test might fail or produce limited results - that's expected
            if result["success"]:
                coverage_stats = self.verify_coverage_requirements(
                    Path(result["output_dir"]), min_coverage=1
                )
                result["coverage_verification"] = coverage_stats

            result["expected_challenge"] = "No common gRNAs between sequences"
            self.test_results.append(result)
            return result

    def test_limited_total_grnas(self):
        """Test with very limited total gRNA budget."""

        result = self.run_ppdesign_command(
            "limited_grnas_test",
            fasta_dir=self.base_dir / "test_fasta",
            min_coverage_per_target=2,
            max_total_grnas=5,  # Very limited
            conservation=0.3,
        )

        if result["success"]:
            coverage_stats = self.verify_coverage_requirements(
                Path(result["output_dir"]), min_coverage=2
            )
            result["coverage_verification"] = coverage_stats

        self.test_results.append(result)
        return result

    def generate_test_report(self) -> str:
        """Generate a comprehensive test report."""

        report = []
        report.append("# Perfect Coverage Mode Test Report")
        report.append(f"Generated on: {pd.Timestamp.now()}")
        report.append("\n## Test Summary\n")

        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r["success"])

        report.append(f"- Total tests run: {total_tests}")
        report.append(f"- Successful tests: {successful_tests}")
        report.append(f"- Failed tests: {total_tests - successful_tests}")
        report.append(f"- Success rate: {successful_tests/total_tests*100:.1f}%\n")

        # Detailed results
        report.append("## Detailed Results\n")

        for result in self.test_results:
            report.append(f"### {result['test_name']}")
            report.append(
                f"- **Status**: {'✅ PASSED' if result['success'] else '❌ FAILED'}"
            )
            report.append(f"- **Return code**: {result['returncode']}")

            if "coverage_verification" in result:
                cv = result["coverage_verification"]
                if "error" not in cv:
                    report.append(f"- **Total guides generated**: {cv['total_guides']}")
                    report.append(
                        f"- **Coverage requirements met**: {'✅ YES' if cv['meets_requirements'] else '❌ NO'}"
                    )

                    if cv["sequences"]:
                        report.append("- **Per-sequence coverage**:")
                        for seq_id, count in cv["sequences"].items():
                            report.append(f"  - {seq_id}: {count} guides")
                else:
                    report.append(f"- **Coverage verification error**: {cv['error']}")

            if "expected_challenge" in result:
                report.append(
                    f"- **Expected challenge**: {result['expected_challenge']}"
                )

            if not result["success"]:
                report.append(f"- **Error output**: ```\n{result['stderr']}\n```")

            report.append("")

        return "\n".join(report)

    def run_all_tests(self):
        """Run all test cases."""

        print("Starting Perfect Coverage Mode Test Suite...")
        print("=" * 60)

        # Run all test cases
        self.test_basic_functionality()
        self.test_high_coverage_requirements()
        self.test_low_conservation_threshold()
        self.test_edge_case_no_common_grnas()
        self.test_limited_total_grnas()

        # Generate and save report
        report_content = self.generate_test_report()

        report_file = self.results_dir / "test_report.md"
        with open(report_file, "w") as f:
            f.write(report_content)

        print("\nTest suite completed!")
        print(f"Report saved to: {report_file}")
        print("\n" + "=" * 60)
        print(report_content)

        return self.test_results


if __name__ == "__main__":
    test_suite = PerfectCoverageTestSuite()
    results = test_suite.run_all_tests()
