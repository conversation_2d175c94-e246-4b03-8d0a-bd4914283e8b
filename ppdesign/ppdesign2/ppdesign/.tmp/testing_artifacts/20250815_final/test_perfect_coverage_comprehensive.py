#!/usr/bin/env python3
"""
Comprehensive test script for perfect coverage mode with real FASTA data.

This script tests the perfect coverage functionality with the actual test sequences
and validates that coverage requirements are properly met.
"""

import sys
from pathlib import Path
from typing import Dict, List, Any
import traceback
from Bio import SeqIO
import datetime

# Add the src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from ppdesign.guide_rna_finder import GuideRNAFinder, GuideRNA

    print("✅ Successfully imported GuideRNAFinder")
except ImportError as e:
    print(f"❌ Failed to import GuideRNAFinder: {e}")
    traceback.print_exc()
    sys.exit(1)


class ComprehensivePerfectCoverageTest:
    """Comprehensive test suite for perfect coverage functionality."""

    def __init__(self):
        self.base_dir = Path(".")
        self.results_dir = Path("results/perfect_coverage_comprehensive")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.test_results = []

    def load_fasta_sequences(self, fasta_file: Path) -> Dict[str, str]:
        """Load sequences from a FASTA file."""
        sequences = {}
        try:
            for record in SeqIO.parse(fasta_file, "fasta"):
                sequences[record.id] = str(record.seq)
            print(f"✅ Loaded {len(sequences)} sequences from {fasta_file}")
            return sequences
        except Exception as e:
            print(f"❌ Failed to load FASTA file {fasta_file}: {e}")
            return {}

    def verify_coverage_requirements(
        self, guides: List[GuideRNA], sequences: Dict[str, str], min_coverage: int
    ) -> Dict[str, Any]:
        """Verify that coverage requirements are met."""

        coverage_stats = {
            "sequences": {},
            "total_guides": len(guides),
            "meets_requirements": True,
            "shortfalls": {},
            "detailed_coverage": {},
        }

        # Count coverage per sequence
        for guide in guides:
            for seq_id in guide.positions.keys():
                coverage_stats["sequences"][seq_id] = (
                    coverage_stats["sequences"].get(seq_id, 0) + 1
                )

        # Check requirements and detailed analysis
        for seq_id in sequences.keys():
            count = coverage_stats["sequences"].get(seq_id, 0)
            coverage_stats["detailed_coverage"][seq_id] = {
                "count": count,
                "requirement_met": count >= min_coverage,
                "shortfall": max(0, min_coverage - count),
            }

            if count < min_coverage:
                coverage_stats["meets_requirements"] = False
                coverage_stats["shortfalls"][seq_id] = min_coverage - count

        return coverage_stats

    def test_with_real_sequences(self):
        """Test perfect coverage with real test sequences."""

        print("\n=== Testing with Real Test Sequences ===")

        fasta_file = self.base_dir / "test_fasta" / "test_small.fna"
        if not fasta_file.exists():
            print(f"❌ Test file not found: {fasta_file}")
            return {
                "test_name": "real_sequences",
                "success": False,
                "error": "File not found",
            }

        sequences = self.load_fasta_sequences(fasta_file)
        if not sequences:
            return {
                "test_name": "real_sequences",
                "success": False,
                "error": "No sequences loaded",
            }

        print(f"Testing with sequences: {list(sequences.keys())}")
        for seq_id, seq in sequences.items():
            print(f"  - {seq_id}: {len(seq)} bp")

        try:
            # Test with different coverage requirements
            test_cases = [
                {"min_coverage": 1, "description": "Low coverage requirement"},
                {"min_coverage": 2, "description": "Medium coverage requirement"},
                {"min_coverage": 3, "description": "High coverage requirement"},
            ]

            results = []

            for case in test_cases:
                print(
                    f"\n--- {case['description']} (min {case['min_coverage']} guides per target) ---"
                )

                finder = GuideRNAFinder(
                    min_conservation=0.1,  # Low conservation to find more guides
                    perfect_coverage=True,
                    min_coverage_per_target=case["min_coverage"],
                    max_total_grnas=30,
                )

                guides = finder._find_perfect_coverage_guides(sequences)
                coverage_stats = self.verify_coverage_requirements(
                    guides, sequences, case["min_coverage"]
                )

                print(f"✅ Found {len(guides)} guide RNAs")
                print(
                    f"Coverage requirements met: {'✅ YES' if coverage_stats['meets_requirements'] else '❌ NO'}"
                )

                for seq_id, details in coverage_stats["detailed_coverage"].items():
                    status = "✅" if details["requirement_met"] else "❌"
                    print(
                        f"  {status} {seq_id}: {details['count']}/{case['min_coverage']} guides"
                    )

                case_result = {
                    "min_coverage": case["min_coverage"],
                    "description": case["description"],
                    "total_guides": len(guides),
                    "coverage_stats": coverage_stats,
                    "success": True,
                }
                results.append(case_result)

            result = {
                "test_name": "real_sequences",
                "success": True,
                "test_cases": results,
                "sequences_tested": list(sequences.keys()),
            }

        except Exception as e:
            print(f"❌ Test failed: {e}")
            traceback.print_exc()
            result = {
                "test_name": "real_sequences",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }

        self.test_results.append(result)
        return result

    def test_coverage_validation(self):
        """Test that the algorithm correctly validates coverage requirements."""

        print("\n=== Testing Coverage Validation ===")

        # Create sequences where only some targets can be satisfied
        sequences = {
            "easy_target": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
            "medium_target": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGCCCG",
            "hard_target": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
        }

        try:
            # Test with impossible requirements
            finder = GuideRNAFinder(
                min_conservation=0.1,
                perfect_coverage=True,
                min_coverage_per_target=10,  # Very high requirement
                max_total_grnas=50,
            )

            guides = finder._find_perfect_coverage_guides(sequences)
            coverage_stats = self.verify_coverage_requirements(guides, sequences, 10)

            print(
                f"✅ Algorithm handled impossible requirements: {len(guides)} guides found"
            )
            print(
                f"Coverage met: {'✅ YES' if coverage_stats['meets_requirements'] else '❌ NO (expected)'}"
            )

            # Analyze which targets got coverage
            covered_targets = sum(
                1
                for details in coverage_stats["detailed_coverage"].values()
                if details["requirement_met"]
            )
            print(
                f"Targets with sufficient coverage: {covered_targets}/{len(sequences)}"
            )

            result = {
                "test_name": "coverage_validation",
                "success": True,
                "total_guides": len(guides),
                "coverage_stats": coverage_stats,
                "test_type": "impossible_requirements",
            }

        except Exception as e:
            print(f"❌ Coverage validation test failed: {e}")
            result = {
                "test_name": "coverage_validation",
                "success": False,
                "error": str(e),
            }

        self.test_results.append(result)
        return result

    def test_algorithm_efficiency(self):
        """Test the efficiency of the greedy algorithm."""

        print("\n=== Testing Algorithm Efficiency ===")

        # Create sequences with known structure
        sequences = {}
        base_seq = "ATCGATCGATCGATCGATCG"

        # Create multiple similar sequences
        for i in range(5):
            # Add some variation
            seq = base_seq + "GGTTTT" + "CCGATCGATCGATCGATCG" + str(i) * 5
            sequences[f"seq_{i+1}"] = seq

        try:
            finder = GuideRNAFinder(
                min_conservation=0.2,
                perfect_coverage=True,
                min_coverage_per_target=2,
                max_total_grnas=15,
            )

            # Test individual algorithm steps
            print("--- Algorithm Steps ---")

            # Step 1: Find all gRNAs
            all_guides_per_seq = finder._find_all_grnas_per_sequence(sequences)
            total_found = sum(len(guides) for guides in all_guides_per_seq.values())
            print(f"✅ Step 1 - Total gRNAs found: {total_found}")

            # Step 2: Create unique set
            unique_guides = finder._create_unique_guide_set(
                all_guides_per_seq, sequences
            )
            print(f"✅ Step 2 - Unique gRNAs created: {len(unique_guides)}")

            # Step 3: Greedy selection
            selected_guides = finder._greedy_set_cover_perfect(unique_guides, sequences)
            print(f"✅ Step 3 - Guides selected: {len(selected_guides)}")

            # Analyze efficiency
            efficiency_stats = {
                "total_found": total_found,
                "unique_created": len(unique_guides),
                "final_selected": len(selected_guides),
                "reduction_ratio": (
                    len(selected_guides) / total_found if total_found > 0 else 0
                ),
                "sequences_count": len(sequences),
            }

            coverage_stats = self.verify_coverage_requirements(
                selected_guides, sequences, 2
            )

            result = {
                "test_name": "algorithm_efficiency",
                "success": True,
                "efficiency_stats": efficiency_stats,
                "coverage_stats": coverage_stats,
            }

            print(
                f"Efficiency: {len(selected_guides)}/{total_found} guides selected ({efficiency_stats['reduction_ratio']:.2%})"
            )

        except Exception as e:
            print(f"❌ Efficiency test failed: {e}")
            result = {
                "test_name": "algorithm_efficiency",
                "success": False,
                "error": str(e),
            }

        self.test_results.append(result)
        return result

    def generate_comprehensive_report(self) -> str:
        """Generate comprehensive test report."""

        report = []
        report.append("# Perfect Coverage Mode Comprehensive Test Report")
        report.append(
            f"Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        report.append("\n## Executive Summary\n")

        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r["success"])

        report.append(f"- **Total test suites run**: {total_tests}")
        report.append(f"- **Successful test suites**: {successful_tests}")
        report.append(f"- **Failed test suites**: {total_tests - successful_tests}")
        report.append(
            f"- **Overall success rate**: {successful_tests/total_tests*100:.1f}%"
        )

        # Algorithm validation summary
        if any(
            r.get("test_name") == "real_sequences" and r.get("success")
            for r in self.test_results
        ):
            report.append("- **Real sequence testing**: ✅ PASSED")
        if any(
            r.get("test_name") == "coverage_validation" and r.get("success")
            for r in self.test_results
        ):
            report.append("- **Coverage validation**: ✅ PASSED")
        if any(
            r.get("test_name") == "algorithm_efficiency" and r.get("success")
            for r in self.test_results
        ):
            report.append("- **Algorithm efficiency**: ✅ PASSED")

        report.append("\n## Detailed Test Results\n")

        for result in self.test_results:
            report.append(f"### {result['test_name'].replace('_', ' ').title()}")
            report.append(
                f"- **Status**: {'✅ PASSED' if result['success'] else '❌ FAILED'}"
            )

            if result["success"]:
                # Real sequences test details
                if "test_cases" in result:
                    report.append("- **Test cases executed**:")
                    for case in result["test_cases"]:
                        meets_req = case["coverage_stats"]["meets_requirements"]
                        status = "✅" if meets_req else "❌"
                        report.append(
                            f"  - {status} {case['description']}: {case['total_guides']} guides"
                        )

                # Coverage validation details
                if "test_type" in result:
                    report.append(f"- **Test type**: {result['test_type']}")
                    if "coverage_stats" in result:
                        covered = sum(
                            1
                            for d in result["coverage_stats"][
                                "detailed_coverage"
                            ].values()
                            if d["requirement_met"]
                        )
                        total = len(result["coverage_stats"]["detailed_coverage"])
                        report.append(
                            f"- **Targets meeting requirements**: {covered}/{total}"
                        )

                # Efficiency details
                if "efficiency_stats" in result:
                    eff = result["efficiency_stats"]
                    report.append("- **Algorithm efficiency**:")
                    report.append(f"  - Total gRNAs found: {eff['total_found']}")
                    report.append(f"  - Unique gRNAs: {eff['unique_created']}")
                    report.append(f"  - Final selection: {eff['final_selected']}")
                    report.append(f"  - Reduction ratio: {eff['reduction_ratio']:.2%}")

            else:
                report.append(f"- **Error**: {result.get('error', 'Unknown error')}")

            report.append("")

        return "\n".join(report)

    def run_comprehensive_tests(self):
        """Run all comprehensive tests."""

        print("Starting Perfect Coverage Mode Comprehensive Test Suite...")
        print("=" * 70)

        # Run all tests
        self.test_with_real_sequences()
        self.test_coverage_validation()
        self.test_algorithm_efficiency()

        # Generate and save report
        report_content = self.generate_comprehensive_report()

        report_file = self.results_dir / "comprehensive_test_report.md"
        with open(report_file, "w") as f:
            f.write(report_content)

        print("\nComprehensive test suite completed!")
        print(f"Report saved to: {report_file}")
        print("\n" + "=" * 70)
        print(report_content)

        return self.test_results


if __name__ == "__main__":
    test_suite = ComprehensivePerfectCoverageTest()
    results = test_suite.run_comprehensive_tests()
