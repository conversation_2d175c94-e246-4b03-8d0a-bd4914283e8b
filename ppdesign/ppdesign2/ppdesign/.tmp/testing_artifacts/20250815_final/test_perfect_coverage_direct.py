#!/usr/bin/env python3
"""
Direct testing of perfect coverage mode functionality.

This script directly imports and tests the perfect coverage implementation
without relying on the CLI interface to avoid dependency issues.
"""

import sys
from pathlib import Path
from typing import Dict, List, Any
import traceback

# Add the src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    # Import the guide RNA finder directly
    from ppdesign.guide_rna_finder import GuideRNAFinder
    from ppdesign.guide_rna_finder import GuideRNA

    print("✅ Successfully imported GuideRNAFinder")
except ImportError as e:
    print(f"❌ Failed to import GuideRNAFinder: {e}")
    traceback.print_exc()
    sys.exit(1)


class DirectPerfectCoverageTest:
    """Direct test of perfect coverage functionality."""

    def __init__(self):
        self.results_dir = Path("results/perfect_coverage_test_direct")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.test_results = []

    def create_test_sequences(
        self, sequences: Dict[str, str], temp_dir: Path = None
    ) -> Dict[str, str]:
        """Create sequences for testing."""
        if temp_dir:
            fasta_dir = temp_dir / "fasta"
            fasta_dir.mkdir(exist_ok=True)
            fasta_file = fasta_dir / "test_sequences.fna"

            with open(fasta_file, "w") as f:
                for seq_id, sequence in sequences.items():
                    f.write(f">{seq_id}\n{sequence}\n")

        return sequences

    def test_perfect_coverage_algorithm(self):
        """Test the perfect coverage algorithm directly."""

        print("\n=== Testing Perfect Coverage Algorithm ===")

        # Create test sequences with known characteristics
        sequences = {
            "seq1": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
            "seq2": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",  # Identical
            "seq3": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCA",  # Slightly different
        }

        try:
            # Initialize the GuideRNAFinder with perfect coverage mode parameters
            finder = GuideRNAFinder(
                min_conservation=0.3,
                perfect_coverage=True,
                min_coverage_per_target=3,
                max_total_grnas=20,
            )

            print("✅ Created GuideRNAFinder instance")
            print(f"   - Conservation threshold: {finder.min_conservation}")
            print(f"   - Perfect coverage mode: {finder.perfect_coverage}")
            print(f"   - Min coverage per target: {finder.min_coverage_per_target}")
            print(f"   - Max total gRNAs: {finder.max_total_grnas}")

            # Test the perfect coverage method directly
            print("\n--- Testing _find_perfect_coverage_guides ---")
            guides = finder._find_perfect_coverage_guides(sequences)

            print(f"✅ Found {len(guides)} guide RNAs")

            # Analyze coverage
            coverage_stats = self._analyze_coverage(guides, sequences)

            result = {
                "test_name": "direct_perfect_coverage",
                "success": True,
                "total_guides": len(guides),
                "coverage_stats": coverage_stats,
                "guides": guides[:5],  # Store first 5 for inspection
            }

            self.test_results.append(result)
            return result

        except Exception as e:
            print(f"❌ Test failed: {e}")
            traceback.print_exc()

            result = {
                "test_name": "direct_perfect_coverage",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
            }

            self.test_results.append(result)
            return result

    def test_edge_case_different_sequences(self):
        """Test with very different sequences."""

        print("\n=== Testing Edge Case: Different Sequences ===")

        # Create very different sequences
        sequences = {
            "seq1": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
            "seq2": "TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT",
            "seq3": "CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC",
        }

        try:
            finder = GuideRNAFinder(
                min_conservation=0.1,  # Very low threshold
                perfect_coverage=True,
                min_coverage_per_target=1,  # Low requirement
                max_total_grnas=10,
            )

            guides = finder._find_perfect_coverage_guides(sequences)
            coverage_stats = self._analyze_coverage(guides, sequences)

            result = {
                "test_name": "edge_case_different_sequences",
                "success": True,
                "total_guides": len(guides),
                "coverage_stats": coverage_stats,
                "expected_challenge": "Very different sequences",
            }

            print(f"✅ Handled edge case: {len(guides)} guides found")

        except Exception as e:
            print(f"❌ Edge case test failed: {e}")
            result = {
                "test_name": "edge_case_different_sequences",
                "success": False,
                "error": str(e),
                "expected_challenge": "Very different sequences",
            }

        self.test_results.append(result)
        return result

    def test_greedy_algorithm_directly(self):
        """Test the greedy set cover algorithm directly."""

        print("\n=== Testing Greedy Set Cover Algorithm ===")

        sequences = {
            "seq1": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
            "seq2": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
            "seq3": "ATCGATCGATCGATCGATCGAGGTTTTCCGATCGATCGATCGATCG",
        }

        try:
            finder = GuideRNAFinder(
                min_conservation=0.8,
                perfect_coverage=True,
                min_coverage_per_target=2,
                max_total_grnas=15,
            )

            # Step 1: Find all gRNAs
            all_guides_per_seq = finder._find_all_grnas_per_sequence(sequences)
            print(
                f"✅ Found gRNAs per sequence: {[len(guides) for guides in all_guides_per_seq.values()]}"
            )

            # Step 2: Create unique guide set
            unique_guides = finder._create_unique_guide_set(
                all_guides_per_seq, sequences
            )
            print(f"✅ Created {len(unique_guides)} unique guides")

            # Step 3: Test greedy algorithm
            selected_guides = finder._greedy_set_cover_perfect(unique_guides, sequences)
            print(f"✅ Greedy algorithm selected {len(selected_guides)} guides")

            coverage_stats = self._analyze_coverage(selected_guides, sequences)

            result = {
                "test_name": "greedy_algorithm_direct",
                "success": True,
                "total_guides": len(selected_guides),
                "coverage_stats": coverage_stats,
                "algorithm_steps": {
                    "total_grnas_found": sum(
                        len(guides) for guides in all_guides_per_seq.values()
                    ),
                    "unique_guides": len(unique_guides),
                    "selected_guides": len(selected_guides),
                },
            }

        except Exception as e:
            print(f"❌ Greedy algorithm test failed: {e}")
            result = {
                "test_name": "greedy_algorithm_direct",
                "success": False,
                "error": str(e),
            }

        self.test_results.append(result)
        return result

    def _analyze_coverage(
        self, guides: List[GuideRNA], sequences: Dict[str, str]
    ) -> Dict[str, Any]:
        """Analyze coverage statistics from guides."""

        coverage_stats = {
            "sequences": {},
            "total_guides": len(guides),
            "meets_requirements": True,
        }

        # Count coverage per sequence
        for guide in guides:
            for seq_id in guide.positions.keys():
                coverage_stats["sequences"][seq_id] = (
                    coverage_stats["sequences"].get(seq_id, 0) + 1
                )

        # Check requirements
        for seq_id in sequences.keys():
            count = coverage_stats["sequences"].get(seq_id, 0)
            print(f"   - {seq_id}: {count} guides")

        return coverage_stats

    def generate_report(self) -> str:
        """Generate test report."""

        report = []
        report.append("# Perfect Coverage Mode Direct Test Report")
        report.append(f"Generated on: {Path(__file__).stat().st_mtime}")
        report.append("\n## Test Summary\n")

        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r["success"])

        report.append(f"- Total tests run: {total_tests}")
        report.append(f"- Successful tests: {successful_tests}")
        report.append(f"- Failed tests: {total_tests - successful_tests}")
        report.append(f"- Success rate: {successful_tests/total_tests*100:.1f}%\n")

        # Detailed results
        report.append("## Detailed Results\n")

        for result in self.test_results:
            report.append(f"### {result['test_name']}")
            report.append(
                f"- **Status**: {'✅ PASSED' if result['success'] else '❌ FAILED'}"
            )

            if result["success"]:
                if "total_guides" in result:
                    report.append(f"- **Total guides**: {result['total_guides']}")

                if "coverage_stats" in result:
                    cs = result["coverage_stats"]
                    report.append("- **Coverage per sequence**:")
                    for seq_id, count in cs["sequences"].items():
                        report.append(f"  - {seq_id}: {count} guides")

                if "algorithm_steps" in result:
                    steps = result["algorithm_steps"]
                    report.append("- **Algorithm performance**:")
                    for step, value in steps.items():
                        report.append(f"  - {step}: {value}")
            else:
                report.append(f"- **Error**: {result.get('error', 'Unknown error')}")

            if "expected_challenge" in result:
                report.append(
                    f"- **Expected challenge**: {result['expected_challenge']}"
                )

            report.append("")

        return "\n".join(report)

    def run_all_tests(self):
        """Run all direct tests."""

        print("Starting Perfect Coverage Mode Direct Test Suite...")
        print("=" * 60)

        # Run tests
        self.test_perfect_coverage_algorithm()
        self.test_edge_case_different_sequences()
        self.test_greedy_algorithm_directly()

        # Generate report
        report_content = self.generate_report()

        report_file = self.results_dir / "direct_test_report.md"
        with open(report_file, "w") as f:
            f.write(report_content)

        print("\nDirect test suite completed!")
        print(f"Report saved to: {report_file}")
        print("\n" + "=" * 60)
        print(report_content)

        return self.test_results


if __name__ == "__main__":
    test_suite = DirectPerfectCoverageTest()
    results = test_suite.run_all_tests()
