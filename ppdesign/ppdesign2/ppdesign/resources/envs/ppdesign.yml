name: probedesign
channels:
  - conda-forge
  - bioconda
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - aws-c-auth=0.7.14=h70caa3e_3
  - aws-c-cal=0.6.9=h14ec70c_3
  - aws-c-common=0.9.12=hd590300_0
  - aws-c-compression=0.2.17=h572eabf_8
  - aws-c-event-stream=0.4.1=h17cd1f3_5
  - aws-c-http=0.8.0=hc6da83f_5
  - aws-c-io=0.14.3=h3c8c088_1
  - aws-c-mqtt=0.10.1=h0ef3971_3
  - aws-c-s3=0.5.0=hb337f33_1
  - aws-c-sdkutils=0.1.14=h572eabf_0
  - aws-checksums=0.1.17=h572eabf_7
  - aws-crt-cpp=0.26.1=h0637f07_8
  - aws-sdk-cpp=1.11.242=h65f022c_0
  - biopython=1.83=py312h98912ed_0
  - blast=2.15.0=pl5321h6f7f691_1
  - blis=0.9.0=hd590300_1
  - bzip2=1.0.8=hd590300_5
  - c-ares=1.26.0=hd590300_0
  - ca-certificates=2023.11.17=hbcca054_0
  - click=8.1.7=unix_pyh707e725_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - curl=8.5.0=hca28451_0
  - diamond=2.1.8=h43eeafb_0
  - entrez-direct=16.2=he881be0_1
  - gawk=5.3.0=ha916aea_0
  - gettext=0.21.1=h27087fc_0
  - gflags=2.2.2=he1b5a44_1004
  - glog=0.6.0=h6f12383_0
  - gmp=6.3.0=h59595ed_0
  - icu=70.1=h27087fc_0
  - keyutils=1.6.1=h166bdaf_0
  - krb5=1.21.2=h659d440_0
  - ld_impl_linux-64=2.40=h41732ed_0
  - libabseil=20230802.1=cxx17_h59595ed_0
  - libarrow=13.0.0=hf0f33ac_25_cpu
  - libblas=3.9.0=21_linux64_openblas
  - libbrotlicommon=1.1.0=hd590300_1
  - libbrotlidec=1.1.0=hd590300_1
  - libbrotlienc=1.1.0=hd590300_1
  - libcblas=3.9.0=21_linux64_openblas
  - libcrc32c=1.1.2=h9c3ff4c_0
  - libcurl=8.5.0=hca28451_0
  - libedit=3.1.20191231=he28a2e2_2
  - libev=4.33=hd590300_2
  - libevent=2.1.12=hf998b51_1
  - libexpat=2.5.0=hcb278e6_1
  - libffi=3.4.2=h7f98852_5
  - libgcc-ng=13.2.0=h807b86a_4
  - libgfortran-ng=13.2.0=h69a702a_4
  - libgfortran5=13.2.0=ha4646dd_4
  - libgomp=13.2.0=h807b86a_4
  - libgoogle-cloud=2.12.0=hef10d8f_5
  - libgrpc=1.60.0=h74775cd_1
  - libiconv=1.17=hd590300_2
  - libidn2=2.3.7=hd590300_0
  - liblapack=3.9.0=21_linux64_openblas
  - libnghttp2=1.58.0=h47da74e_1
  - libnl=3.9.0=hd590300_0
  - libnsl=2.0.1=hd590300_0
  - libnuma=2.0.16=h0b41bf4_1
  - libopenblas=0.3.26=pthreads_h413a1c8_0
  - libprotobuf=4.25.1=hf27288f_0
  - libre2-11=2023.06.02=h7a70373_0
  - libsqlite=3.44.2=h2797004_0
  - libssh2=1.11.0=h0841786_0
  - libstdcxx-ng=13.2.0=h7e041cc_4
  - libthrift=0.19.0=hb90f79a_1
  - libunistring=0.9.10=h7f98852_0
  - libutf8proc=2.8.0=h166bdaf_0
  - libuuid=2.38.1=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libxml2=2.9.14=h22db469_4
  - libzlib=1.2.13=hd590300_5
  - lz4-c=1.9.4=hcb278e6_0
  - mafft=7.520=h031d066_3
  - markdown-it-py=3.0.0=pyhd8ed1ab_0
  - mdurl=0.1.2=pyhd8ed1ab_0
  - mpfr=4.2.1=h9458935_0
  - ncbi-vdb=3.0.10=hdbdd923_0
  - ncurses=6.4=h59595ed_2
  - numpy=1.26.3=py312heda63a1_0
  - openssl=3.2.1=hd590300_0
  - orc=1.9.2=h7829240_1
  - ossuuid=1.6.2=hf484d3e_1000
  - packaging=23.2=pyhd8ed1ab_0
  - pandas=2.2.0=py312hfb8ada1_0
  - pcre=8.45=h9c3ff4c_0
  - perl=5.32.1=7_hd590300_perl5
  - perl-alien-build=2.48=pl5321hec16e2b_0
  - perl-alien-libxml2=0.17=pl5321hec16e2b_0
  - perl-archive-tar=2.40=pl5321hdfd78af_0
  - perl-business-isbn=3.007=pl5321hdfd78af_0
  - perl-business-isbn-data=20210112.006=pl5321hdfd78af_0
  - perl-capture-tiny=0.48=pl5321hdfd78af_2
  - perl-carp=1.38=pl5321hdfd78af_4
  - perl-common-sense=3.75=pl5321hdfd78af_0
  - perl-compress-raw-bzip2=2.201=pl5321h87f3376_1
  - perl-compress-raw-zlib=2.105=pl5321h87f3376_0
  - perl-constant=1.33=pl5321hdfd78af_2
  - perl-data-dumper=2.183=pl5321hec16e2b_1
  - perl-encode=3.19=pl5321hec16e2b_1
  - perl-exporter=5.72=pl5321hdfd78af_2
  - perl-exporter-tiny=1.002002=pl5321hdfd78af_0
  - perl-extutils-makemaker=7.70=pl5321hd8ed1ab_0
  - perl-ffi-checklib=0.28=pl5321hdfd78af_0
  - perl-file-chdir=0.1010=pl5321hdfd78af_3
  - perl-file-path=2.18=pl5321hd8ed1ab_0
  - perl-file-temp=0.2304=pl5321hd8ed1ab_0
  - perl-file-which=1.24=pl5321hd8ed1ab_0
  - perl-importer=0.026=pl5321hdfd78af_0
  - perl-io-compress=2.201=pl5321hdbdd923_2
  - perl-io-zlib=1.14=pl5321hdfd78af_0
  - perl-json=4.10=pl5321hdfd78af_0
  - perl-json-xs=2.34=pl5321h4ac6f70_6
  - perl-list-moreutils=0.430=pl5321hdfd78af_0
  - perl-list-moreutils-xs=0.430=pl5321h031d066_2
  - perl-mime-base64=3.16=pl5321hec16e2b_2
  - perl-parent=0.236=pl5321hdfd78af_2
  - perl-path-tiny=0.122=pl5321hdfd78af_0
  - perl-pathtools=3.75=pl5321hec16e2b_3
  - perl-scalar-list-utils=1.62=pl5321hec16e2b_1
  - perl-scope-guard=0.21=pl5321hdfd78af_3
  - perl-sub-info=0.002=pl5321hdfd78af_1
  - perl-term-table=0.016=pl5321hdfd78af_0
  - perl-test2-suite=0.000145=pl5321hdfd78af_0
  - perl-types-serialiser=1.01=pl5321hdfd78af_0
  - perl-uri=5.12=pl5321hdfd78af_0
  - perl-xml-libxml=2.0207=pl5321h661654b_0
  - perl-xml-namespacesupport=1.12=pl5321hdfd78af_1
  - perl-xml-sax=1.02=pl5321hdfd78af_1
  - perl-xml-sax-base=1.09=pl5321hdfd78af_1
  - pip=23.3.2=pyhd8ed1ab_0
  - polars=0.20.6=py312hfa2e56e_0
  - proteinortho=6.3.1=h70414c8_0
  - pyarrow=13.0.0=py312h176e3d2_25_cpu
  - pygments=2.17.2=pyhd8ed1ab_0
  - python=3.12.1=hab00c5b_1_cpython
  - python-dateutil=2.8.2=pyhd8ed1ab_0
  - python-tzdata=2023.4=pyhd8ed1ab_0
  - python_abi=3.12=4_cp312
  - pytz=2023.4=pyhd8ed1ab_0
  - rdma-core=50.0=hd3aeb46_0
  - re2=2023.06.02=h2873b5e_0
  - readline=8.2=h8228510_1
  - rich=13.7.0=pyhd8ed1ab_0
  - s2n=1.4.3=h06160fa_0
  - setuptools=69.0.3=pyhd8ed1ab_0
  - shellingham=1.5.4=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - snappy=1.1.10=h9fff704_0
  - tk=8.6.13=noxft_h4845f30_101
  - tqdm=4.66.1=pyhd8ed1ab_0
  - typer=0.9.0=pyhd8ed1ab_0
  - typing-extensions=4.9.0=hd8ed1ab_0
  - typing_extensions=4.9.0=pyha770c72_0
  - tzdata=2023d=h0c530f3_0
  - ucx=1.15.0=h75e419f_3
  - wget=1.20.3=ha35d2d1_1
  - wheel=0.42.0=pyhd8ed1ab_0
  - xz=5.2.6=h166bdaf_0
  - zlib=1.2.13=hd590300_5
  - zstd=1.5.5=hfc55251_0
prefix: /global/cfs/cdirs/nelli/frederik/conda/probedesign
