# MAXGROUPS Error Fix Documentation

## Problem Description

When running pixi commands, users may encounter the following error:

```
NameError: name 'MAXGROUPS' is not defined
```

This error occurs in the Python standard library's regex module:
```
File ".pixi/envs/default/lib/python3.12/re/_parser.py", line 89, in opengroup
    if self.groups > MAXGROUPS:
                     ^^^^^^^^^
NameError: name 'MAXGROUPS' is not defined
```

## Root Cause

This error indicates corruption in the pixi Python environment, specifically:
- Missing constants in the `re._constants` module
- Corrupted standard library files
- Incomplete or interrupted environment installation
- Version mismatches between Python components

## Solution Methods

### Method 1: Quick Patch (Temporary)

If you need an immediate fix:

```bash
# Add the missing constant to the re module
echo "MAXGROUPS = 2147483647" >> .pixi/envs/default/lib/python3.12/re/_constants.py
```

⚠️ **Warning**: This is a temporary fix. The environment likely has other issues.

### Method 2: Environment Recreation (Recommended)

The proper solution is to recreate the pixi environment:

```bash
# Step 1: Clean pixi cache
pixi clean

# Step 2: Remove corrupted environment
rm -rf .pixi/envs/default

# Step 3: Reinstall environment
pixi install
```

### Method 3: Alternative Execution

If the environment remains problematic, you can run scripts directly:

1. **Modify pixi.toml** to run scripts directly instead of as modules:

```toml
# Instead of:
msa-view = { cmd = "python -m ppdesign.terminal_msa_viewer view", env = { PYTHONPATH = "src" } }

# Use:
msa-view = { cmd = "python src/ppdesign/terminal_msa_viewer.py view" }
```


## Prevention

To prevent this issue:

1. **Always use pixi for environment management**:
   ```bash
   pixi install  # Instead of manual pip installs
   ```

2. **Keep pixi updated**:
   ```bash
   pixi self-update
   ```

3. **Check environment health regularly**:
   ```bash
   pixi info
   ```

4. **Use lock files**:
   - Commit `pixi.lock` to version control
   - This ensures reproducible environments

## Verification

After fixing, verify the environment works:

```bash
# Test Python import system
pixi run python -c "import re; print('✓ re module works')"

# Test a pixi task
pixi run msa-stats

# If using ppdesign
pixi run ppdesign-grna --help
```

## Troubleshooting Other Related Issues

### SSL Module Errors

If you see:
```
AttributeError: module 'ssl' has no attribute 'SSLWantReadError'
```

This indicates deeper corruption. Use Method 2 (Environment Recreation).

### Import Chain Issues

If specific packages fail to import:
- Check for circular imports in `__init__.py` files
- Consider lazy imports for heavy dependencies
- Run scripts directly instead of as modules

### Pixi Environment Variables

Ensure proper environment setup:
```bash
# Check current environment
pixi info

# Verify Python path
pixi run python -c "import sys; print(sys.path)"
```

## Working Example

Here's a complete working example after fixing:

```bash
# Fresh environment setup
rm -rf .pixi
pixi init
pixi add python numpy pandas biopython rich typer

# Create a test script
cat > test_env.py << 'EOF'
import re
import sys
print(f"Python: {sys.version}")
print(f"MAXGROUPS: {getattr(re._constants, 'MAXGROUPS', 'NOT FOUND')}")
print("✓ Environment working!")
EOF

# Run test
pixi run python test_env.py
```

Expected output:
```
Python: 3.12.x
MAXGROUPS: 2147483647
✓ Environment working!
```

## Additional Resources

- [Pixi Documentation](https://pixi.sh/docs)
- [Python re module documentation](https://docs.python.org/3/library/re.html)
- [PPDesign Issues](https://github.com/ppdesign/ppdesign/issues)

## Contact

If the issue persists after trying these solutions, please:
1. Check the PPDesign GitHub issues
2. Create a new issue with:
   - Full error traceback
   - Output of `pixi info`
   - Your operating system and Python version
   - Steps to reproduce the error
