#!/usr/bin/env python3
"""Validate gRNA matches against target sequences."""

import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from Bio import SeqIO
from Bio.Seq import Seq
import pandas as pd
import re


def find_grna_matches(grna_seq, target_seq, allow_mismatches=0):
    """Find all matches of gRNA in target sequence (both strands)."""
    matches = []
    target_seq = target_seq.upper()
    grna_seq = grna_seq.upper()

    # Check forward strand
    for i in range(len(target_seq) - len(grna_seq) + 1):
        target_segment = target_seq[i : i + len(grna_seq)]
        mismatches = sum(1 for a, b in zip(grna_seq, target_segment) if a != b)
        if mismatches <= allow_mismatches:
            # Check for PAM (NGG or NAG)
            if i + len(grna_seq) + 2 <= len(target_seq):
                pam = target_seq[i + len(grna_seq) : i + len(grna_seq) + 3]
                if re.match(r"[ATCG]GG|[ATCG]AG", pam):
                    matches.append(
                        {
                            "position": i + 1,  # 1-based
                            "strand": "+",
                            "sequence": target_segment,
                            "pam": pam,
                            "mismatches": mismatches,
                            "match_percent": (len(grna_seq) - mismatches)
                            / len(grna_seq)
                            * 100,
                        }
                    )

    # Check reverse strand
    rev_comp = str(Seq(target_seq).reverse_complement())
    for i in range(len(rev_comp) - len(grna_seq) + 1):
        target_segment = rev_comp[i : i + len(grna_seq)]
        mismatches = sum(1 for a, b in zip(grna_seq, target_segment) if a != b)
        if mismatches <= allow_mismatches:
            # Check for PAM (NGG or NAG)
            if i + len(grna_seq) + 2 <= len(rev_comp):
                pam = rev_comp[i + len(grna_seq) : i + len(grna_seq) + 3]
                if re.match(r"[ATCG]GG|[ATCG]AG", pam):
                    # Convert position to original sequence coordinates
                    orig_pos = len(target_seq) - i - len(grna_seq) + 1
                    matches.append(
                        {
                            "position": orig_pos,
                            "strand": "-",
                            "sequence": target_segment,
                            "pam": pam,
                            "mismatches": mismatches,
                            "match_percent": (len(grna_seq) - mismatches)
                            / len(grna_seq)
                            * 100,
                        }
                    )

    return matches


def main():
    # Load test sequences
    test_fasta = "tests/test.fna"
    sequences = {}
    for record in SeqIO.parse(test_fasta, "fasta"):
        sequences[record.id] = str(record.seq)

    # Load guide RNA results
    csv_file = "results/results/validation_run/guide_rnas.csv"
    df = pd.read_csv(csv_file)

    # Analyze top 5 gRNAs
    report = []
    report.append("# gRNA Validation Report\n")
    report.append("## Analysis Parameters")
    report.append("- Conservation threshold: 50%")
    report.append("- Non-degenerate mode: Enabled (exact sequences only)")
    report.append("- Total sequences analyzed: 26")
    report.append(f"- Total gRNAs found: {len(df)}\n")

    # Detailed analysis of top 5
    report.append("## Top 5 gRNA Detailed Validation\n")

    for idx in range(min(5, len(df))):
        row = df.iloc[idx]
        grna_id = row["Guide_ID"]
        grna_seq = row["Sequence"]
        conservation = row["Conservation"]
        target_count = row["Target_Count"]
        quality_score = row["Quality_Score"]

        report.append(f"### {idx + 1}. {grna_id}")
        report.append(f"**Sequence:** `{grna_seq}`")
        report.append(f"**PAM:** {row['PAM']}")
        report.append(f"**Conservation:** {conservation} ({target_count}/26 sequences)")
        report.append(f"**Quality Score:** {quality_score:.3f}\n")

        # Validate matches
        exact_matches = 0
        total_matches = 0
        match_details = []

        for seq_id, seq in sequences.items():
            matches = find_grna_matches(grna_seq, seq, allow_mismatches=5)
            if matches:
                total_matches += 1
                best_match = min(matches, key=lambda x: x["mismatches"])
                if best_match["mismatches"] == 0:
                    exact_matches += 1
                    match_details.append(
                        f"- ✅ **{seq_id}**: Perfect match at position {best_match['position']} ({best_match['strand']} strand)"
                    )
                else:
                    match_details.append(
                        f"- ⚠️ **{seq_id}**: {best_match['match_percent']:.1f}% match ({best_match['mismatches']} mismatches) at position {best_match['position']}"
                    )

        report.append("**Validation Results:**")
        report.append(
            f"- Exact matches: {exact_matches}/{target_count} claimed targets"
        )
        report.append(f"- Total sequences with matches: {total_matches}/26")
        report.append(f"- Actual conservation: {total_matches/26*100:.1f}%")

        if total_matches < target_count:
            report.append(
                f"- ⚠️ **Warning:** Found only {total_matches} matches but {target_count} were claimed"
            )

        report.append("\n**Match Details (first 10):**")
        for detail in match_details[:10]:
            report.append(detail)

        if len(match_details) > 10:
            report.append(f"- ... and {len(match_details) - 10} more matches")

        report.append("")

    # Summary statistics
    report.append("\n## Summary Statistics\n")

    # Check all gRNAs
    perfect_match_grnas = 0
    for idx in range(len(df)):
        row = df.iloc[idx]
        grna_seq = row["Sequence"]
        target_count = row["Target_Count"]

        exact_matches = 0
        for seq_id, seq in sequences.items():
            matches = find_grna_matches(grna_seq, seq, allow_mismatches=0)
            if matches:
                exact_matches += 1

        if exact_matches == target_count:
            perfect_match_grnas += 1

    report.append(
        f"- gRNAs with 100% exact matches to claimed targets: {perfect_match_grnas}/{len(df)}"
    )
    report.append(
        f"- Average conservation across all gRNAs: {df['Conservation'].str.rstrip('%').astype(float).mean():.1f}%"
    )
    report.append(
        f"- gRNAs with conservation ≥ 70%: {len(df[df['Conservation'].str.rstrip('%').astype(float) >= 70])}"
    )
    report.append(
        f"- gRNAs with conservation ≥ 60%: {len(df[df['Conservation'].str.rstrip('%').astype(float) >= 60])}"
    )
    report.append(
        f"- gRNAs with conservation ≥ 50%: {len(df[df['Conservation'].str.rstrip('%').astype(float) >= 50])}"
    )

    # Print report
    print("\n".join(report))

    # Save report
    with open("results/validation_report.md", "w") as f:
        f.write("\n".join(report))

    print("\n📊 Report saved to results/validation_report.md")


if __name__ == "__main__":
    main()
