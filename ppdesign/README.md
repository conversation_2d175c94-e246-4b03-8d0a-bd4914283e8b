# PPDesign - <PERSON>be and Primer Design Pipeline

[![<PERSON><PERSON>](https://img.shields.io/badge/Pixi-Enabled-blue)](https://pixi.sh)
[![Python](https://img.shields.io/badge/python-3.11%2B-blue)](https://www.python.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

PPDesign is a modern bioinformatics pipeline for designing oligonucleotide probes and primers targeting specific taxonomic groups. It offers two complementary approaches: gene-based design using codon alignments and direct nucleotide-based design using k-mer analysis.

## 🧬 Key Features

- **Dual-mode operation**: Gene-based (Mode 1) and direct nucleotide (Mode 2) probe design
- **Database integration**: Direct access to NeLLi genome database with 700K+ bacterial/archaeal genomes
- **Advanced algorithms**: K-mer seeding, MSA-based conservation, and codon-aware alignment
- **Comprehensive filtering**: GC content, melting temperature, secondary structure checks
- **Degenerate nucleotide support**: IUPAC codes for capturing sequence variation
- **Parallel processing**: Multi-threaded design for large-scale analyses
- **Visualization**: Probe binding site mapping and ranking

## 🔬 Pipeline Architecture

```mermaid
graph TB
    %% Define styles
    classDef inputStyle fill:#e1f5e1,stroke:#4caf50,stroke-width:3px,color:#1b5e20
    classDef mode1Style fill:#e3f2fd,stroke:#2196f3,stroke-width:3px,color:#0d47a1
    classDef mode2Style fill:#fce4ec,stroke:#e91e63,stroke-width:3px,color:#880e4f
    classDef processStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100
    classDef outputStyle fill:#f3e5f5,stroke:#9c27b0,stroke-width:3px,color:#4a148c
    classDef dbStyle fill:#e0f2f1,stroke:#009688,stroke-width:3px,color:#004d40

    %% Input sources
    subgraph inputs["📥 Input Sources"]
        DB[(NeLLi Genome<br/>Database<br/>700K+ genomes)]:::dbStyle
        LOCAL[Local FASTA<br/>Files]:::inputStyle
        TAXA[Taxonomic<br/>Query]:::inputStyle
    end

    %% Mode 1: Gene-based
    subgraph mode1["🧬 Mode 1: Gene-Based Design"]
        DOWNLOAD[Download<br/>Genomes]:::mode1Style
        GENECALL[Gene Calling<br/>prodigal-gv]:::mode1Style
        ORTHO[Ortholog Detection<br/>ProteinOrtho]:::mode1Style
        ALIGN[Protein Alignment<br/>MAFFT]:::mode1Style
        CODON[Codon Alignment<br/>pal2nal]:::mode1Style
    end

    %% Mode 2: Direct nucleotide
    subgraph mode2["🔤 Mode 2: Direct Nucleotide Design"]
        KMER[K-mer Analysis<br/>Seed & Extend]:::mode2Style
        MSA[MSA Analysis<br/>MUSCLE]:::mode2Style
        MINIMAP[Minimap2 Alignment<br/>Contigs/Genomes]:::mode2Style
        CONSERVE[Conservation<br/>Detection]:::mode2Style
    end

    %% Shared processing
    subgraph process["⚙️ Probe Processing & Filtering"]
        THERMO[Thermodynamic<br/>Filtering<br/>GC%, Tm]:::processStyle
        STRUCT[Secondary Structure<br/>Check<br/>Hairpins, Dimers]:::processStyle
        DEGEN[Degenerate Base<br/>Generation<br/>IUPAC Codes]:::processStyle
        QUALITY[Quality Scoring<br/>& Ranking]:::processStyle
    end

    %% Outputs
    subgraph outputs["📊 Outputs"]
        PROBES[Selected<br/>Probes/Primers]:::outputStyle
        VIZ[Binding Site<br/>Visualization]:::outputStyle
        REPORT[Analysis<br/>Report]:::outputStyle
    end

    %% Connections
    DB --> |Query| TAXA
    TAXA --> DOWNLOAD
    LOCAL --> GENECALL
    DOWNLOAD --> GENECALL
    
    GENECALL --> |FAA| ORTHO
    ORTHO --> |Groups| ALIGN
    ALIGN --> |Aligned| CODON
    CODON --> THERMO
    
    LOCAL --> |Direct| KMER
    LOCAL --> |Direct| MSA
    LOCAL --> |Direct| MINIMAP
    KMER --> CONSERVE
    MSA --> CONSERVE
    MINIMAP --> CONSERVE
    CONSERVE --> THERMO
    
    THERMO --> STRUCT
    STRUCT --> DEGEN
    DEGEN --> QUALITY
    QUALITY --> PROBES
    QUALITY --> VIZ
    QUALITY --> REPORT
```

## 🚀 Installation

### Using Pixi (Recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/ppdesign.git
cd ppdesign

# Install with pixi
pixi install

# Activate the environment
pixi shell
```

### Using pip

```bash
# Clone the repository
git clone https://github.com/yourusername/ppdesign.git
cd ppdesign

# Install in development mode
pip install -e ".[dev]"
```

## 📖 Usage

### Mode 1: Gene-Based Design (for bacteria and viruses)

This mode performs gene calling, finds orthologous groups, and designs probes from codon alignments.

#### Database Query Mode
```bash
# Design probes for bacterial genus Citrobacter
ppdesign-unified \
  --db-query \
  --taxonomy "g__Citrobacter" \
  --tax-level species \
  --conservation 0.3 \
  --threads 8 \
  --limit 10 \
  -o citrobacter_probes

# Design probes for a specific bacterial species
ppdesign-unified \
  --db-query \
  --taxonomy "s__Escherichia coli" \
  --tax-level strain \
  --conservation 0.8 \
  --threads 8 \
  --limit 50 \
  -o ecoli_probes
```

#### Local File Mode
```bash
# Use your own genome files
ppdesign-unified \
  --fasta-dir path/to/genomes/ \
  --conservation 0.3 \
  --threads 8 \
  -o my_probes
```

### Mode 2: Direct Nucleotide Design (no gene calling)

This mode finds conserved regions directly from nucleotide sequences using k-mer or MSA approaches.

#### K-mer Based Method (recommended for longer sequences)
```bash
# Design 20bp probes with specific thermodynamic properties
ppdesign-nucleotide \
  --fasta-dir path/to/sequences/ \
  --method kmer \
  --kmer-size 20 \
  --conservation 0.8 \
  --gc-min 40 --gc-max 60 \
  --tm-min 55 --tm-max 65 \
  --min-length 18 --max-length 25 \
  --threads 8 \
  -o nucleotide_probes
```

#### MSA Based Method (for short, highly similar sequences like amplicons)
```bash
# Note: Uses MUSCLE for alignment, best for sequences <1kb with high similarity
ppdesign-nucleotide \
  --fasta-dir path/to/amplicons/ \
  --method msa \
  --conservation 0.9 \
  --window-size 30 \
  --step-size 5 \
  --threads 8 \
  -o amplicon_probes
```

#### Minimap2 Based Method (for contigs and genomes)
```bash
# Note: Uses minimap2 for fast alignment of longer sequences (genomes, contigs)
# Ideal for finding conserved regions across divergent sequences
ppdesign-nucleotide \
  --fasta-dir path/to/genomes/ \
  --method minimap2 \
  --conservation 0.8 \
  --gc-min 45 --gc-max 60 \
  --tm-min 55 --tm-max 65 \
  --min-length 20 --max-length 25 \
  --threads 8 \
  -o genome_probes
```

### Post-Processing Tools

#### Oligonucleotide Selection and Filtering
```bash
ppdesign-select \
  -i results/my_probes/codon_alignments \
  --gc-range 45 55 \
  --tm-range 58 62 \
  --length 20 25 \
  --window-size 25 \
  --step 5 \
  --threshold 0.8 \
  --threads 8
```

#### Probe Ranking and Visualization
```bash
ppdesign-rank \
  -o results/my_probes/selected_oligos.csv \
  -g reference_genome.fna \
  -r results/my_probes/rankings
```

## 📊 Output Structure

All results are organized in the `results/` directory:

```
results/
└── your_run_name/
    ├── data/
    │   ├── fna/              # Downloaded/input genomes
    │   ├── faa/              # Protein sequences
    │   └── fnn/              # Gene nucleotide sequences
    ├── proteinortho/         # Orthologous groups
    ├── alignments/           # MAFFT alignments
    ├── codon_alignments/     # Codon alignments
    ├── selected_oligos.csv   # Final probe designs
    └── conserved_regions.txt # Detailed conservation info
```

## 🔧 Advanced Parameters

### Conservation Levels
- `0.3` (30%): Low conservation, more degenerate probes
- `0.5` (50%): Moderate conservation, balanced specificity
- `0.8` (80%): High conservation, specific probes

### Thermodynamic Parameters
- **GC Content**: 40-60% (optimal for PCR)
- **Melting Temperature**: 55-65°C (standard PCR conditions)
- **Probe Length**: 18-25bp (optimal for specificity)

### Secondary Structure Checks
- **Hairpin ΔG**: > -3.0 kcal/mol
- **Self-dimer ΔG**: > -6.0 kcal/mol

## 🧪 Examples

### Example 1: Bacterial Genus Probe Design
```bash
# Design probes for Salmonella genus
ppdesign-unified \
  --db-query \
  --taxonomy "g__Salmonella" \
  --tax-level species \
  --conservation 0.5 \
  --threads 16 \
  --limit 20 \
  -o salmonella_probes
```

### Example 2: Viral Sequence Probe Design
```bash
# Design probes from viral amplicon sequences
ppdesign-nucleotide \
  --fasta-dir viral_amplicons/ \
  --method kmer \
  --kmer-size 20 \
  --conservation 0.7 \
  --gc-min 35 --gc-max 65 \
  --tm-min 50 --tm-max 70 \
  --threads 8 \
  -o viral_probes
```

### Example 3: Genome-Based Probe Design with Minimap2
```bash
# Design probes from bacterial genome assemblies using minimap2
ppdesign-nucleotide \
  --fasta-dir bacterial_genomes/ \
  --method minimap2 \
  --conservation 0.7 \
  --gc-min 50 --gc-max 65 \
  --tm-min 58 --tm-max 62 \
  --min-length 22 --max-length 25 \
  --threads 16 \
  -o genome_conserved_probes
```

### Example 4: High-Specificity Probe Design
```bash
# Design highly specific probes with strict filtering
ppdesign-unified \
  --db-query \
  --taxonomy "s__Mycobacterium tuberculosis" \
  --conservation 0.9 \
  --threads 8 \
  --limit 30 \
  -o mtb_specific_probes

# Apply strict filtering
ppdesign-select \
  -i results/mtb_specific_probes/codon_alignments \
  --gc-range 50 60 \
  --tm-range 60 65 \
  --length 22 25 \
  --threshold 0.95 \
  --threads 8
```

## 🛠️ Development

### Running Tests
```bash
pixi run test
```

### Code Quality
```bash
pixi run lint      # Run linters
pixi run format    # Format code
pixi run type-check # Type checking
```

### Building Documentation
```bash
pixi run docs       # Serve locally
pixi run build-docs # Build static
```

## 📁 Project Structure

```
ppdesign/
├── src/ppdesign/          # Source code
│   ├── probedesign_unified.py      # Mode 1: Gene-based
│   ├── probedesign_nucleotide.py   # Mode 2: Direct nucleotide
│   ├── conserved_finder.py         # Conservation algorithms
│   ├── genome_database.py          # Database interface
│   ├── probedesign_seqselection.py # Oligo filtering
│   ├── probedesign_rank.py         # Ranking & visualization
│   └── kmer_finder.py              # K-mer utilities
├── tests/                 # Test suite
├── docs/                  # Documentation
├── examples/              # Example data
├── resources/             # Resources
├── results/              # Output directory (gitignored)
├── pixi.toml             # Environment config
├── pyproject.toml        # Package config
└── README.md             # This file
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📚 Citation

If you use PPDesign in your research, please cite:

```bibtex
@software{ppdesign2024,
  title = {PPDesign: A Modern Pipeline for Oligonucleotide Probe and Primer Design},
  year = {2024},
  url = {https://github.com/yourusername/ppdesign}
}
```